import sys
import random
import time
import json
import openpyxl
from openpyxl import Workbook
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton,
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox,
                             QProgressBar, QSlider, QFrame, QStackedWidget,
                             QTabWidget, QTextEdit, QFileDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor

class ExcelGeneratorThread(QThread):
    """Excel生成线程"""
    progress_updated = pyqtSignal(int)
    finished_signal = pyqtSignal(str)
    
    def __init__(self, settings):
        super().__init__()
        self.settings = settings
    
    def run(self):
        """生成Excel文件"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "小数运算练习题"
            
            total_problems = self.settings['total_problems']
            problems_per_group = self.settings['problems_per_group']
            decimal_places = self.settings['decimal_places']
            operators = self.settings['operators']
            difficulty = self.settings['difficulty']
            
            # 计算组数
            num_groups = total_problems // problems_per_group
            
            # 生成所有题目
            all_problems = []
            for i in range(total_problems):
                problem = self.generate_problem(decimal_places, operators, difficulty)
                all_problems.append(problem)
                
                # 更新进度
                progress = int((i + 1) / total_problems * 50)  # 生成占50%
                self.progress_updated.emit(progress)
            
            # 写入Excel
            col_index = 1
            for group in range(num_groups):
                start_idx = group * problems_per_group
                group_problems = all_problems[start_idx:start_idx + problems_per_group]
                
                # 写入表头
                group_name = f"第{group+1}组"
                ws.cell(row=1, column=col_index, value=f"{group_name}_数字A")
                ws.cell(row=1, column=col_index+1, value=f"{group_name}_运算符")
                ws.cell(row=1, column=col_index+2, value=f"{group_name}_数字B")
                ws.cell(row=1, column=col_index+3, value=f"{group_name}_等号")
                ws.cell(row=1, column=col_index+4, value=f"{group_name}_结果")
                
                # 写入数据
                for row_idx, problem in enumerate(group_problems, start=2):
                    ws.cell(row=row_idx, column=col_index, value=problem[0])      # 数字A
                    ws.cell(row=row_idx, column=col_index+1, value=problem[1])    # 运算符
                    ws.cell(row=row_idx, column=col_index+2, value=problem[2])    # 数字B
                    ws.cell(row=row_idx, column=col_index+3, value=problem[3])    # 等号
                    ws.cell(row=row_idx, column=col_index+4, value=problem[4])    # 结果
                
                # 移动到下一组
                col_index += 6
                
                # 更新进度
                progress = 50 + int((group + 1) / num_groups * 50)  # 写入占50%
                self.progress_updated.emit(progress)
            
            # 保存文件
            filename = f'小数运算练习_{decimal_places}位小数_{total_problems}题.xlsx'
            wb.save(filename)
            
            self.finished_signal.emit(filename)
            
        except Exception as e:
            self.finished_signal.emit(f"错误: {str(e)}")
    
    def generate_problem(self, decimal_places, operators, difficulty):
        """生成单个题目"""
        # 根据难度设置数值范围
        ranges = {1: (0.1, 2.9), 2: (0.1, 4.9), 3: (0.1, 9.9), 
                 4: (0.1, 19.9), 5: (0.1, 99.9)}
        min_val, max_val = ranges[difficulty]
        
        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        op = random.choice(operators)
        
        # 确保结果合理
        if op == '-' and a < b:
            a, b = b, a
        elif op == '÷':
            if b > a:
                a, b = b, a
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)
        
        # 计算结果
        if op == '+':
            result = round(a + b, decimal_places)
        elif op == '-':
            result = round(a - b, decimal_places)
        elif op == '×':
            result = round(a * b, decimal_places)
        else:  # ÷
            result = round(a / b, decimal_places)
        
        return [a, op, b, '=', result]

class GameStats:
    """游戏统计系统"""
    def __init__(self):
        self.reset()
        self.load_stats()
    
    def reset(self):
        self.score = 0
        self.total_questions = 0
        self.correct_answers = 0
        self.streak = 0
        self.max_streak = 0
        self.total_time = 0
        self.best_time = float('inf')
        self.level = 1
        self.exp = 0
        self.exp_to_next = 100
        self.achievements = set()
    
    def add_correct_answer(self, answer_time, difficulty):
        """添加正确答案"""
        self.correct_answers += 1
        self.total_questions += 1
        self.streak += 1
        self.max_streak = max(self.max_streak, self.streak)
        self.total_time += answer_time
        self.best_time = min(self.best_time, answer_time)
        
        # 计算得分
        base_score = 10
        difficulty_bonus = difficulty * 5
        speed_bonus = max(0, int((5 - answer_time) * 2))
        streak_bonus = min(self.streak * 2, 50)
        
        score_gained = base_score + difficulty_bonus + speed_bonus + streak_bonus
        self.score += score_gained
        
        # 计算经验值
        exp_gained = 5 + difficulty * 2 + max(0, int((3 - answer_time) * 3))
        level_up = self.add_exp(exp_gained)
        
        # 检查成就
        self.check_achievements(answer_time)
        
        self.save_stats()
        return score_gained, exp_gained, level_up
    
    def add_wrong_answer(self):
        """添加错误答案"""
        self.total_questions += 1
        self.streak = 0
        self.save_stats()
    
    def add_exp(self, exp):
        """添加经验值"""
        self.exp += exp
        level_up = False
        while self.exp >= self.exp_to_next:
            self.exp -= self.exp_to_next
            self.level += 1
            self.exp_to_next = int(self.exp_to_next * 1.2)
            level_up = True
        return level_up
    
    def get_accuracy(self):
        """获取正确率"""
        if self.total_questions == 0:
            return 0
        return (self.correct_answers / self.total_questions) * 100
    
    def check_achievements(self, answer_time):
        """检查成就"""
        new_achievements = []
        
        # 首次答对
        if self.correct_answers == 1 and 'first_correct' not in self.achievements:
            self.achievements.add('first_correct')
            new_achievements.append('🎯 初出茅庐 - 答对第一题')
        
        # 连击成就
        if self.streak == 5 and 'streak_5' not in self.achievements:
            self.achievements.add('streak_5')
            new_achievements.append('🔥 连击新手 - 连续答对5题')
        
        if self.streak == 10 and 'streak_10' not in self.achievements:
            self.achievements.add('streak_10')
            new_achievements.append('⚡ 连击高手 - 连续答对10题')
        
        if self.streak == 20 and 'streak_20' not in self.achievements:
            self.achievements.add('streak_20')
            new_achievements.append('💫 连击大师 - 连续答对20题')
        
        # 速度成就
        if answer_time < 3 and 'speed_demon' not in self.achievements:
            self.achievements.add('speed_demon')
            new_achievements.append('⚡ 闪电侠 - 3秒内答对一题')
        
        # 数量成就
        if self.correct_answers == 100 and 'century' not in self.achievements:
            self.achievements.add('century')
            new_achievements.append('💯 百题达人 - 累计答对100题')
        
        # 准确率成就
        if self.get_accuracy() >= 95 and self.total_questions >= 20 and 'perfectionist' not in self.achievements:
            self.achievements.add('perfectionist')
            new_achievements.append('🏆 完美主义者 - 正确率达到95%')
        
        return new_achievements
    
    def save_stats(self):
        """保存统计数据"""
        try:
            data = {
                'score': self.score,
                'total_questions': self.total_questions,
                'correct_answers': self.correct_answers,
                'max_streak': self.max_streak,
                'best_time': self.best_time,
                'level': self.level,
                'exp': self.exp,
                'exp_to_next': self.exp_to_next,
                'achievements': list(self.achievements)
            }
            with open('game_stats.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def load_stats(self):
        """加载统计数据"""
        try:
            with open('game_stats.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.score = data.get('score', 0)
                self.total_questions = data.get('total_questions', 0)
                self.correct_answers = data.get('correct_answers', 0)
                self.max_streak = data.get('max_streak', 0)
                self.best_time = data.get('best_time', float('inf'))
                self.level = data.get('level', 1)
                self.exp = data.get('exp', 0)
                self.exp_to_next = data.get('exp_to_next', 100)
                self.achievements = set(data.get('achievements', []))
        except:
            pass

class EnhancedMathGame(QMainWindow):
    """增强版数学游戏"""
    
    def __init__(self):
        super().__init__()
        
        # 游戏数据
        self.stats = GameStats()
        self.current_problem = {}
        self.start_time = 0
        self.current_mode = "classic"
        self.time_limit = 0
        
        # 定时器
        self.game_timer = QTimer()
        self.game_timer.timeout.connect(self.update_timer)
        
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle('🎮 数学大冒险 - 增强版')
        self.setGeometry(100, 100, 1000, 700)
        
        # 中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 状态栏
        self.create_status_bar(main_layout)
        
        # 标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 游戏标签页
        self.setup_game_tab()
        
        # Excel生成标签页
        self.setup_excel_tab()
        
        # 统计标签页
        self.setup_stats_tab()
        
        # 成就标签页
        self.setup_achievements_tab()
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        # 等级和经验
        self.level_label = QLabel(f"🏆 等级: {self.stats.level}")
        self.level_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.level_label)
        
        self.exp_bar = QProgressBar()
        self.exp_bar.setMaximumWidth(150)
        self.exp_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid white;
                border-radius: 5px;
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #FFD700;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.exp_bar)
        
        # 分数和连击
        self.score_label = QLabel(f"💎 得分: {self.stats.score}")
        self.score_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.score_label)
        
        self.streak_label = QLabel("🔥 连击: 0")
        self.streak_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.streak_label)
        
        # 时间
        self.time_label = QLabel("⏱️ 时间: 00:00")
        self.time_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.time_label)
        
        layout.addWidget(status_frame)
        
        # 初始化状态显示
        self.update_status_display()

    def setup_game_tab(self):
        """设置游戏标签页"""
        game_widget = QWidget()
        self.tab_widget.addTab(game_widget, "🎮 游戏模式")

        layout = QVBoxLayout(game_widget)

        # 模式选择
        mode_group = QGroupBox("🎯 选择游戏模式")
        mode_layout = QGridLayout(mode_group)

        # 经典模式
        classic_btn = QPushButton("📚 经典模式\n自由练习，稳步提升")
        classic_btn.setMinimumHeight(80)
        classic_btn.clicked.connect(lambda: self.start_game_mode("classic"))
        mode_layout.addWidget(classic_btn, 0, 0)

        # 挑战模式
        challenge_btn = QPushButton("⚡ 挑战模式\n60秒限时挑战")
        challenge_btn.setMinimumHeight(80)
        challenge_btn.clicked.connect(lambda: self.start_game_mode("challenge"))
        mode_layout.addWidget(challenge_btn, 0, 1)

        # 闯关模式
        adventure_btn = QPushButton("🏰 闯关模式\n逐级解锁新挑战")
        adventure_btn.setMinimumHeight(80)
        adventure_btn.clicked.connect(lambda: self.start_game_mode("adventure"))
        mode_layout.addWidget(adventure_btn, 1, 0)

        # 竞速模式
        race_btn = QPushButton("🏁 竞速模式\n速度与准确的较量")
        race_btn.setMinimumHeight(80)
        race_btn.clicked.connect(lambda: self.start_game_mode("race"))
        mode_layout.addWidget(race_btn, 1, 1)

        layout.addWidget(mode_group)

        # 题目显示
        problem_frame = QFrame()
        problem_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f093fb, stop:1 #f5576c);
                border-radius: 20px;
                margin: 10px;
                min-height: 120px;
            }
        """)
        problem_layout = QVBoxLayout(problem_frame)

        self.problem_label = QLabel("选择游戏模式开始挑战")
        self.problem_label.setFont(QFont("Microsoft YaHei", 28, QFont.Bold))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setStyleSheet("color: white; padding: 20px;")
        problem_layout.addWidget(self.problem_label)

        self.difficulty_label = QLabel("🌟 难度: 简单")
        self.difficulty_label.setFont(QFont("Microsoft YaHei", 14))
        self.difficulty_label.setAlignment(Qt.AlignCenter)
        self.difficulty_label.setStyleSheet("color: white; margin-bottom: 10px;")
        problem_layout.addWidget(self.difficulty_label)

        layout.addWidget(problem_frame)

        # 设置区域
        settings_group = QGroupBox("⚙️ 游戏设置")
        settings_layout = QGridLayout(settings_group)

        # 小数位数
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(1, 3)
        self.decimal_places.setValue(1)
        settings_layout.addWidget(self.decimal_places, 0, 1)

        # 难度滑块
        settings_layout.addWidget(QLabel("难度等级:"), 0, 2)
        self.difficulty_slider = QSlider(Qt.Horizontal)
        self.difficulty_slider.setRange(1, 5)
        self.difficulty_slider.setValue(1)
        self.difficulty_slider.valueChanged.connect(self.update_difficulty_display)
        settings_layout.addWidget(self.difficulty_slider, 0, 3)

        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        operators_layout = QHBoxLayout()

        self.add_cb = QCheckBox('➕ 加法')
        self.add_cb.setChecked(True)
        self.sub_cb = QCheckBox('➖ 减法')
        self.sub_cb.setChecked(True)
        self.mul_cb = QCheckBox('✖️ 乘法')
        self.div_cb = QCheckBox('➗ 除法')

        for cb in [self.add_cb, self.sub_cb, self.mul_cb, self.div_cb]:
            operators_layout.addWidget(cb)

        settings_layout.addLayout(operators_layout, 1, 1, 1, 3)
        layout.addWidget(settings_group)

        # 答案输入
        answer_frame = QFrame()
        answer_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 15px;
                padding: 15px;
                margin: 10px;
            }
        """)
        answer_layout = QHBoxLayout(answer_frame)

        answer_layout.addWidget(QLabel("💡 你的答案:"))

        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Microsoft YaHei", 16))
        self.answer_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 10px;
                font-size: 16px;
                background-color: #F8F9FA;
            }
            QLineEdit:focus {
                border: 2px solid #4A90E2;
                background-color: white;
            }
        """)
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)

        layout.addWidget(answer_frame)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始游戏")
        self.check_btn = QPushButton("✅ 检查答案")
        self.next_btn = QPushButton("⏭️ 下一题")
        self.hint_btn = QPushButton("💡 提示")

        for btn in [self.start_btn, self.check_btn, self.next_btn, self.hint_btn]:
            btn.setMinimumHeight(45)
            button_layout.addWidget(btn)

        self.start_btn.clicked.connect(self.start_game)
        self.check_btn.clicked.connect(self.check_answer)
        self.next_btn.clicked.connect(self.next_question)
        self.hint_btn.clicked.connect(self.show_hint)

        # 初始状态
        self.check_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.hint_btn.setEnabled(False)

        layout.addLayout(button_layout)

        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumHeight(50)
        layout.addWidget(self.result_label)

    def setup_excel_tab(self):
        """设置Excel生成标签页"""
        excel_widget = QWidget()
        self.tab_widget.addTab(excel_widget, "📊 Excel生成")

        layout = QVBoxLayout(excel_widget)

        # 标题
        title = QLabel("📊 Excel练习题生成器")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #333; margin: 20px;")
        layout.addWidget(title)

        # 设置区域
        settings_group = QGroupBox("⚙️ 生成设置")
        settings_layout = QGridLayout(settings_group)

        # 题目总数
        settings_layout.addWidget(QLabel("题目总数:"), 0, 0)
        self.excel_total_problems = QSpinBox()
        self.excel_total_problems.setRange(100, 5000)
        self.excel_total_problems.setValue(1000)
        self.excel_total_problems.setSuffix(" 题")
        settings_layout.addWidget(self.excel_total_problems, 0, 1)

        # 每组题数
        settings_layout.addWidget(QLabel("每组题数:"), 0, 2)
        self.excel_problems_per_group = QSpinBox()
        self.excel_problems_per_group.setRange(10, 50)
        self.excel_problems_per_group.setValue(20)
        self.excel_problems_per_group.setSuffix(" 题")
        settings_layout.addWidget(self.excel_problems_per_group, 0, 3)

        # 小数位数
        settings_layout.addWidget(QLabel("小数位数:"), 1, 0)
        self.excel_decimal_places = QSpinBox()
        self.excel_decimal_places.setRange(1, 3)
        self.excel_decimal_places.setValue(1)
        self.excel_decimal_places.setSuffix(" 位")
        settings_layout.addWidget(self.excel_decimal_places, 1, 1)

        # 难度等级
        settings_layout.addWidget(QLabel("难度等级:"), 1, 2)
        self.excel_difficulty = QSlider(Qt.Horizontal)
        self.excel_difficulty.setRange(1, 5)
        self.excel_difficulty.setValue(1)
        settings_layout.addWidget(self.excel_difficulty, 1, 3)

        # 运算符选择
        operators_group = QGroupBox("运算符选择")
        operators_layout = QHBoxLayout(operators_group)

        self.excel_add_cb = QCheckBox('➕ 加法')
        self.excel_add_cb.setChecked(True)
        self.excel_sub_cb = QCheckBox('➖ 减法')
        self.excel_sub_cb.setChecked(True)
        self.excel_mul_cb = QCheckBox('✖️ 乘法')
        self.excel_div_cb = QCheckBox('➗ 除法')

        for cb in [self.excel_add_cb, self.excel_sub_cb, self.excel_mul_cb, self.excel_div_cb]:
            operators_layout.addWidget(cb)

        settings_layout.addWidget(operators_group, 2, 0, 1, 4)
        layout.addWidget(settings_group)

        # 进度显示
        self.excel_progress = QProgressBar()
        self.excel_progress.setVisible(False)
        layout.addWidget(self.excel_progress)

        # 生成按钮
        generate_btn = QPushButton("🚀 生成Excel文件")
        generate_btn.setMinimumHeight(50)
        generate_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border: none;
                border-radius: 10px;
                color: white;
                font: bold 16px;
                padding: 15px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
        """)
        generate_btn.clicked.connect(self.generate_excel)
        layout.addWidget(generate_btn)

        # 预览区域
        preview_group = QGroupBox("📋 题目预览")
        preview_layout = QVBoxLayout(preview_group)

        self.excel_preview = QTextEdit()
        self.excel_preview.setMaximumHeight(200)
        self.excel_preview.setReadOnly(True)
        preview_layout.addWidget(self.excel_preview)

        preview_btn = QPushButton("👀 预览题目")
        preview_btn.clicked.connect(self.preview_problems)
        preview_layout.addWidget(preview_btn)

        layout.addWidget(preview_group)
        layout.addStretch()

    def setup_stats_tab(self):
        """设置统计标签页"""
        stats_widget = QWidget()
        self.tab_widget.addTab(stats_widget, "📈 统计数据")

        layout = QVBoxLayout(stats_widget)

        # 标题
        title = QLabel("📈 游戏统计数据")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #333; margin: 20px;")
        layout.addWidget(title)

        # 统计信息
        stats_group = QGroupBox("📊 总体统计")
        stats_layout = QGridLayout(stats_group)

        self.stats_labels = {}
        stats_items = [
            ("🏆 当前等级", f"{self.stats.level}"),
            ("💎 总得分", f"{self.stats.score}"),
            ("📝 总题数", f"{self.stats.total_questions}"),
            ("✅ 答对题数", f"{self.stats.correct_answers}"),
            ("📈 正确率", f"{self.stats.get_accuracy():.1f}%"),
            ("🔥 最高连击", f"{self.stats.max_streak}"),
            ("⚡ 最快用时", f"{self.stats.best_time:.1f}秒" if self.stats.best_time != float('inf') else "暂无"),
            ("⏱️ 平均用时", f"{self.stats.total_time/max(self.stats.correct_answers,1):.1f}秒" if self.stats.correct_answers > 0 else "暂无")
        ]

        for i, (label, value) in enumerate(stats_items):
            row, col = i // 2, (i % 2) * 2

            label_widget = QLabel(label)
            label_widget.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
            stats_layout.addWidget(label_widget, row, col)

            value_widget = QLabel(value)
            value_widget.setFont(QFont("Microsoft YaHei", 12))
            value_widget.setStyleSheet("color: #4A90E2; font-weight: bold;")
            stats_layout.addWidget(value_widget, row, col + 1)

            self.stats_labels[label] = value_widget

        layout.addWidget(stats_group)

        # 重置按钮
        reset_btn = QPushButton("🔄 重置统计数据")
        reset_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6B6B, stop:1 #FF5252);
                border: none;
                border-radius: 10px;
                color: white;
                font: bold 14px;
                padding: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF7B7B, stop:1 #FF6B6B);
            }
        """)
        reset_btn.clicked.connect(self.reset_stats)
        layout.addWidget(reset_btn)

        layout.addStretch()

    def setup_achievements_tab(self):
        """设置成就标签页"""
        achievements_widget = QWidget()
        self.tab_widget.addTab(achievements_widget, "🏆 成就系统")

        layout = QVBoxLayout(achievements_widget)

        # 标题
        title = QLabel("🏆 成就系统")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #333; margin: 20px;")
        layout.addWidget(title)

        # 成就列表
        achievements_group = QGroupBox("🎯 成就列表")
        achievements_layout = QVBoxLayout(achievements_group)

        achievements_list = [
            ("first_correct", "🎯 初出茅庐", "答对第一题"),
            ("streak_5", "🔥 连击新手", "连续答对5题"),
            ("streak_10", "⚡ 连击高手", "连续答对10题"),
            ("streak_20", "💫 连击大师", "连续答对20题"),
            ("speed_demon", "⚡ 闪电侠", "3秒内答对一题"),
            ("century", "💯 百题达人", "累计答对100题"),
            ("perfectionist", "🏆 完美主义者", "正确率达到95%")
        ]

        for achievement_id, name, desc in achievements_list:
            achievement_frame = QFrame()
            achievement_frame.setStyleSheet("""
                QFrame {
                    border: 2px solid #E0E0E0;
                    border-radius: 10px;
                    padding: 10px;
                    margin: 5px;
                    background-color: #F8F9FA;
                }
            """)

            if achievement_id in self.stats.achievements:
                achievement_frame.setStyleSheet("""
                    QFrame {
                        border: 2px solid #4CAF50;
                        border-radius: 10px;
                        padding: 10px;
                        margin: 5px;
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #E8F5E8, stop:1 #C8E6C9);
                    }
                """)

            achievement_layout = QHBoxLayout(achievement_frame)

            name_label = QLabel(name)
            name_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
            achievement_layout.addWidget(name_label)

            desc_label = QLabel(desc)
            desc_label.setFont(QFont("Microsoft YaHei", 12))
            desc_label.setStyleSheet("color: #666;")
            achievement_layout.addWidget(desc_label)

            if achievement_id in self.stats.achievements:
                status_label = QLabel("✅ 已解锁")
                status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            else:
                status_label = QLabel("🔒 未解锁")
                status_label.setStyleSheet("color: #999; font-weight: bold;")

            achievement_layout.addWidget(status_label)
            achievements_layout.addWidget(achievement_frame)

        layout.addWidget(achievements_group)
        layout.addStretch()

    def start_game_mode(self, mode):
        """开始游戏模式"""
        self.current_mode = mode

        # 设置时间限制
        if mode == "challenge":
            self.time_limit = 60
            self.problem_label.setText("⚡ 挑战模式 - 60秒限时挑战")
        elif mode == "race":
            self.time_limit = 30
            self.problem_label.setText("🏁 竞速模式 - 30秒极速挑战")
        elif mode == "adventure":
            self.problem_label.setText("🏰 闯关模式 - 逐级挑战")
            # 根据等级调整难度
            self.difficulty_slider.setValue(min(5, (self.stats.level - 1) // 5 + 1))
        else:
            self.time_limit = 0
            self.problem_label.setText("📚 经典模式 - 自由练习")

        # 启用开始按钮
        self.start_btn.setEnabled(True)
        self.check_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.hint_btn.setEnabled(False)

    def start_game(self):
        """开始游戏"""
        self.start_btn.setEnabled(False)
        self.check_btn.setEnabled(True)
        self.hint_btn.setEnabled(True)

        if self.time_limit > 0:
            self.game_timer.start(1000)

        self.generate_problem()

    def generate_problem(self):
        """生成题目"""
        decimal_places = self.decimal_places.value()
        difficulty = self.difficulty_slider.value()

        # 根据难度设置数值范围
        ranges = {1: (0.1, 2.9), 2: (0.1, 4.9), 3: (0.1, 9.9),
                 4: (0.1, 19.9), 5: (0.1, 99.9)}
        min_val, max_val = ranges[difficulty]

        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)

        # 获取运算符
        operators = []
        if self.add_cb.isChecked(): operators.append('+')
        if self.sub_cb.isChecked(): operators.append('-')
        if self.mul_cb.isChecked(): operators.append('×')
        if self.div_cb.isChecked(): operators.append('÷')

        if not operators:
            operators = ['+']

        op = random.choice(operators)

        # 确保结果合理
        if op == '-' and a < b:
            a, b = b, a
        elif op == '÷':
            if b > a:
                a, b = b, a
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)

        # 计算答案
        if op == '+':
            answer = a + b
        elif op == '-':
            answer = a - b
        elif op == '×':
            answer = a * b
        else:  # ÷
            answer = a / b

        self.current_problem = {
            'a': a, 'b': b, 'op': op, 'answer': round(answer, decimal_places)
        }

        # 显示题目
        self.problem_label.setText(f"{a} {op} {b} = ?")
        self.start_time = time.time()
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()
        self.next_btn.setEnabled(True)

    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            correct_answer = self.current_problem['answer']
            answer_time = time.time() - self.start_time
            difficulty = self.difficulty_slider.value()

            if abs(user_answer - correct_answer) < 0.001:
                # 答对了
                score_gained, exp_gained, level_up = self.stats.add_correct_answer(answer_time, difficulty)

                # 显示结果
                time_text = f"{answer_time:.1f}秒"
                if answer_time < 3:
                    time_text += " ⚡闪电般！"
                elif answer_time < 5:
                    time_text += " 🚀很快！"

                self.result_label.setText(
                    f"🎉 正确！答案是 {correct_answer}\n"
                    f"⏱️ 用时: {time_text}\n"
                    f"💎 得分: +{score_gained} ✨ 经验: +{exp_gained}"
                )
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        background: #E8F5E8;
                        border: 2px solid #4CAF50;
                        border-radius: 10px;
                        padding: 10px;
                    }
                """)

                # 检查升级
                if level_up:
                    QMessageBox.information(
                        self, "🎉 升级！",
                        f"恭喜升级到 {self.stats.level} 级！\n"
                        f"🌟 解锁了更多功能和挑战！"
                    )

                # 检查新成就
                new_achievements = self.stats.check_achievements(answer_time)
                for achievement in new_achievements:
                    QMessageBox.information(
                        self, "🏆 成就解锁！",
                        f"🎉 恭喜解锁成就：\n{achievement}"
                    )

            else:
                # 答错了
                self.stats.add_wrong_answer()
                self.result_label.setText(f"❌ 错误！正确答案是 {correct_answer}\n💪 继续加油！")
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #F44336;
                        background: #FFEBEE;
                        border: 2px solid #F44336;
                        border-radius: 10px;
                        padding: 10px;
                    }
                """)

            self.update_status_display()
            self.update_stats_display()

        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

    def next_question(self):
        """下一题"""
        self.generate_problem()

    def show_hint(self):
        """显示提示"""
        problem = self.current_problem
        difficulty = self.difficulty_slider.value()

        if difficulty <= 2:
            # 简单提示
            if problem['op'] == '+':
                hint = f"💡 提示：{problem['a']} + {problem['b']}\n把两个数相加！"
            elif problem['op'] == '-':
                hint = f"💡 提示：{problem['a']} - {problem['b']}\n用大数减小数！"
            elif problem['op'] == '×':
                hint = f"💡 提示：{problem['a']} × {problem['b']}\n两个数相乘！"
            else:
                hint = f"💡 提示：{problem['a']} ÷ {problem['b']}\n第一个数除以第二个数！"
        else:
            # 困难提示
            answer_str = str(problem['answer'])
            if len(answer_str) > 1:
                hint_answer = answer_str[0] + "?" * (len(answer_str) - 1)
                hint = f"💡 提示：答案开头是 {answer_str[0]}\n完整答案：{hint_answer}"
            else:
                hint = "💡 提示：答案是一位数！"

        QMessageBox.information(self, "💡 智能提示", hint)

    def update_timer(self):
        """更新计时器"""
        if self.time_limit > 0:
            self.time_limit -= 1
            minutes = self.time_limit // 60
            seconds = self.time_limit % 60
            self.time_label.setText(f"⏱️ 剩余: {minutes:02d}:{seconds:02d}")

            if self.time_limit <= 0:
                self.end_timed_mode()
        else:
            # 普通计时
            elapsed = int(time.time() - self.start_time) if hasattr(self, 'start_time') else 0
            minutes = elapsed // 60
            seconds = elapsed % 60
            self.time_label.setText(f"⏱️ 时间: {minutes:02d}:{seconds:02d}")

    def end_timed_mode(self):
        """结束限时模式"""
        self.game_timer.stop()

        mode_name = "挑战" if self.current_mode == "challenge" else "竞速"

        QMessageBox.information(
            self,
            f"⏰ {mode_name}结束！",
            f"🎯 {mode_name}模式结束！\n\n"
            f"📊 最终统计：\n"
            f"✅ 答对: {self.stats.correct_answers} 题\n"
            f"💎 总得分: {self.stats.score}\n"
            f"🔥 最高连击: {self.stats.max_streak}\n"
            f"📈 正确率: {self.stats.get_accuracy():.1f}%\n"
            f"⚡ 最快用时: {self.stats.best_time:.1f}秒\n\n"
            f"🏆 {'表现优秀！' if self.stats.get_accuracy() >= 80 else '继续加油！'}"
        )

    def generate_excel(self):
        """生成Excel文件"""
        # 获取设置
        operators = []
        if self.excel_add_cb.isChecked(): operators.append('+')
        if self.excel_sub_cb.isChecked(): operators.append('-')
        if self.excel_mul_cb.isChecked(): operators.append('×')
        if self.excel_div_cb.isChecked(): operators.append('÷')

        if not operators:
            QMessageBox.warning(self, "设置错误", "请至少选择一个运算符！")
            return

        settings = {
            'total_problems': self.excel_total_problems.value(),
            'problems_per_group': self.excel_problems_per_group.value(),
            'decimal_places': self.excel_decimal_places.value(),
            'operators': operators,
            'difficulty': self.excel_difficulty.value()
        }

        # 显示进度条
        self.excel_progress.setVisible(True)
        self.excel_progress.setValue(0)

        # 创建生成线程
        self.excel_thread = ExcelGeneratorThread(settings)
        self.excel_thread.progress_updated.connect(self.excel_progress.setValue)
        self.excel_thread.finished_signal.connect(self.excel_generation_finished)
        self.excel_thread.start()

    def excel_generation_finished(self, result):
        """Excel生成完成"""
        self.excel_progress.setVisible(False)

        if result.startswith("错误"):
            QMessageBox.critical(self, "生成失败", result)
        else:
            QMessageBox.information(
                self, "生成成功",
                f"✅ Excel文件生成成功！\n\n"
                f"📁 文件名: {result}\n"
                f"📊 格式: 每个题目占用5个单元格\n"
                f"📋 结构: 数字A | 运算符 | 数字B | 等号 | 结果\n\n"
                f"🎉 文件已保存到当前目录！"
            )

    def preview_problems(self):
        """预览题目"""
        operators = []
        if self.excel_add_cb.isChecked(): operators.append('+')
        if self.excel_sub_cb.isChecked(): operators.append('-')
        if self.excel_mul_cb.isChecked(): operators.append('×')
        if self.excel_div_cb.isChecked(): operators.append('÷')

        if not operators:
            operators = ['+']

        decimal_places = self.excel_decimal_places.value()
        difficulty = self.excel_difficulty.value()

        preview_text = "📋 题目预览（前10题）：\n" + "="*40 + "\n"

        for i in range(10):
            # 生成题目
            ranges = {1: (0.1, 2.9), 2: (0.1, 4.9), 3: (0.1, 9.9),
                     4: (0.1, 19.9), 5: (0.1, 99.9)}
            min_val, max_val = ranges[difficulty]

            a = round(random.uniform(min_val, max_val), decimal_places)
            b = round(random.uniform(min_val, max_val), decimal_places)
            op = random.choice(operators)

            # 确保结果合理
            if op == '-' and a < b:
                a, b = b, a
            elif op == '÷':
                if b > a:
                    a, b = b, a
                result = round(a / b, decimal_places)
                b = round(a / result, decimal_places)

            # 计算结果
            if op == '+':
                result = round(a + b, decimal_places)
            elif op == '-':
                result = round(a - b, decimal_places)
            elif op == '×':
                result = round(a * b, decimal_places)
            else:  # ÷
                result = round(a / b, decimal_places)

            preview_text += f"{i+1:2d}. {a} {op} {b} = {result}\n"

        preview_text += "="*40 + "\n"
        preview_text += f"💡 Excel格式：每题占5列\n"
        preview_text += f"📊 列结构：数字A | 运算符 | 数字B | 等号 | 结果"

        self.excel_preview.setText(preview_text)

    def update_status_display(self):
        """更新状态显示"""
        self.level_label.setText(f"🏆 等级: {self.stats.level}")

        progress = (self.stats.exp / self.stats.exp_to_next) * 100
        self.exp_bar.setValue(int(progress))
        self.exp_bar.setFormat(f"{self.stats.exp}/{self.stats.exp_to_next} EXP")

        self.score_label.setText(f"💎 得分: {self.stats.score}")
        self.streak_label.setText(f"🔥 连击: {self.stats.streak}")

    def update_stats_display(self):
        """更新统计显示"""
        if hasattr(self, 'stats_labels'):
            self.stats_labels["🏆 当前等级"].setText(f"{self.stats.level}")
            self.stats_labels["💎 总得分"].setText(f"{self.stats.score}")
            self.stats_labels["📝 总题数"].setText(f"{self.stats.total_questions}")
            self.stats_labels["✅ 答对题数"].setText(f"{self.stats.correct_answers}")
            self.stats_labels["📈 正确率"].setText(f"{self.stats.get_accuracy():.1f}%")
            self.stats_labels["🔥 最高连击"].setText(f"{self.stats.max_streak}")
            self.stats_labels["⚡ 最快用时"].setText(f"{self.stats.best_time:.1f}秒" if self.stats.best_time != float('inf') else "暂无")
            self.stats_labels["⏱️ 平均用时"].setText(f"{self.stats.total_time/max(self.stats.correct_answers,1):.1f}秒" if self.stats.correct_answers > 0 else "暂无")

    def update_difficulty_display(self):
        """更新难度显示"""
        difficulty_names = {
            1: "🌟 简单", 2: "🌟🌟 较简单", 3: "🌟🌟🌟 中等",
            4: "🌟🌟🌟🌟 较难", 5: "🌟🌟🌟🌟🌟 困难"
        }
        difficulty = self.difficulty_slider.value()
        self.difficulty_label.setText(f"难度: {difficulty_names.get(difficulty, '🌟 简单')}")

    def reset_stats(self):
        """重置统计数据"""
        reply = QMessageBox.question(
            self, "确认重置",
            "⚠️ 确定要重置所有统计数据吗？\n\n"
            "这将清除：\n"
            "• 等级和经验值\n"
            "• 得分和答题记录\n"
            "• 所有成就\n\n"
            "此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.stats.reset()
            self.update_status_display()
            self.update_stats_display()
            QMessageBox.information(self, "重置完成", "📊 统计数据已重置！")

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
            QWidget {
                font-family: "Microsoft YaHei";
            }
            QTabWidget::pane {
                border: 1px solid #C0C0C0;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E1E1E1, stop:1 #DDDDDD);
                border: 2px solid #C4C4C3;
                border-bottom-color: #C2C7CB;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fafafa, stop:1 #f4f4f4);
                border-color: #9B9B9B;
                border-bottom-color: #C2C7CB;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                border: none;
                border-radius: 8px;
                color: white;
                font: bold 12px;
                padding: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5BA0F2, stop:1 #4A90E2);
            }
            QPushButton:disabled {
                background: #CCCCCC;
                color: #666666;
            }
            QGroupBox {
                font: bold 14px;
                border: 2px solid #E0E0E0;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #FAFAFA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #333;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("数学大冒险")
    app.setApplicationVersion("2.0")

    game = EnhancedMathGame()
    game.show()

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
