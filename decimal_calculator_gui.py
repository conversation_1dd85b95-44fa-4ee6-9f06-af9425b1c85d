import sys
import random
from PyQt5.QtWidgets import (QA<PERSON>lication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton, 
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class DecimalCalculatorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_a = 0
        self.current_b = 0
        self.current_op = '+'
        self.correct_answer = 0
        self.initUI()
        self.generate_new_problem()
    
    def initUI(self):
        self.setWindowTitle('小数运算练习器')
        self.setGeometry(300, 300, 500, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 设置组
        settings_group = QGroupBox("设置")
        settings_layout = QGridLayout(settings_group)
        
        # 小数位数设置
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places_spinbox = QSpinBox()
        self.decimal_places_spinbox.setRange(1, 3)
        self.decimal_places_spinbox.setValue(1)
        self.decimal_places_spinbox.valueChanged.connect(self.generate_new_problem)
        settings_layout.addWidget(self.decimal_places_spinbox, 0, 1)
        
        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        
        operators_layout = QHBoxLayout()
        self.add_checkbox = QCheckBox('+')
        self.add_checkbox.setChecked(True)
        self.subtract_checkbox = QCheckBox('-')
        self.subtract_checkbox.setChecked(True)
        self.multiply_checkbox = QCheckBox('×')
        self.multiply_checkbox.setChecked(False)
        self.divide_checkbox = QCheckBox('÷')
        self.divide_checkbox.setChecked(False)
        
        operators_layout.addWidget(self.add_checkbox)
        operators_layout.addWidget(self.subtract_checkbox)
        operators_layout.addWidget(self.multiply_checkbox)
        operators_layout.addWidget(self.divide_checkbox)
        
        # 连接复选框信号
        self.add_checkbox.stateChanged.connect(self.generate_new_problem)
        self.subtract_checkbox.stateChanged.connect(self.generate_new_problem)
        self.multiply_checkbox.stateChanged.connect(self.generate_new_problem)
        self.divide_checkbox.stateChanged.connect(self.generate_new_problem)
        
        settings_layout.addLayout(operators_layout, 1, 1)
        
        main_layout.addWidget(settings_group)
        
        # 题目显示区域
        problem_group = QGroupBox("题目")
        problem_layout = QVBoxLayout(problem_group)
        
        # 题目显示
        self.problem_label = QLabel()
        self.problem_label.setFont(QFont("Arial", 24))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 20px; border: 2px solid #ccc; }")
        problem_layout.addWidget(self.problem_label)
        
        # 答案输入
        answer_layout = QHBoxLayout()
        answer_layout.addWidget(QLabel("答案:"))
        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Arial", 16))
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)
        
        problem_layout.addLayout(answer_layout)
        
        main_layout.addWidget(problem_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.check_button = QPushButton("检查答案")
        self.check_button.clicked.connect(self.check_answer)
        self.check_button.setFont(QFont("Arial", 12))
        button_layout.addWidget(self.check_button)
        
        self.next_button = QPushButton("下一题")
        self.next_button.clicked.connect(self.generate_new_problem)
        self.next_button.setFont(QFont("Arial", 12))
        button_layout.addWidget(self.next_button)
        
        main_layout.addLayout(button_layout)
        
        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Arial", 14))
        self.result_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.result_label)
    
    def generate_decimal_numbers(self):
        """根据设置生成两个小数"""
        decimal_places = self.decimal_places_spinbox.value()
        min_val = 0.1
        max_val = 9.9
        
        # 根据小数位数调整范围
        if decimal_places == 2:
            max_val = 9.99
        elif decimal_places == 3:
            max_val = 9.999
        
        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        return a, b
    
    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_checkbox.isChecked():
            operators.append('+')
        if self.subtract_checkbox.isChecked():
            operators.append('-')
        if self.multiply_checkbox.isChecked():
            operators.append('×')
        if self.divide_checkbox.isChecked():
            operators.append('÷')
        
        if not operators:
            # 如果没有选中任何运算符，默认选择加法
            operators.append('+')
            self.add_checkbox.setChecked(True)
        
        return operators
    
    def ensure_positive_result(self, a, b, op):
        """确保减法结果不为负数，除法结果为整数或简单小数"""
        if op == '-' and a < b:
            return b, a
        elif op == '÷':
            # 确保除法结果是合理的小数
            if b > a:
                a, b = b, a
            # 调整b使得结果是简单的小数
            decimal_places = self.decimal_places_spinbox.value()
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)
        return a, b
    
    def calculate_answer(self, a, b, op):
        """计算正确答案"""
        if op == '+':
            return a + b
        elif op == '-':
            return a - b
        elif op == '×':
            return a * b
        elif op == '÷':
            return a / b
        return 0
    
    def generate_new_problem(self):
        """生成新题目"""
        self.current_a, self.current_b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        self.current_op = random.choice(operators)
        
        self.current_a, self.current_b = self.ensure_positive_result(
            self.current_a, self.current_b, self.current_op)
        
        self.correct_answer = self.calculate_answer(
            self.current_a, self.current_b, self.current_op)
        
        # 显示题目
        problem_text = f"{self.current_a} {self.current_op} {self.current_b} = ?"
        self.problem_label.setText(problem_text)
        
        # 清空输入和结果
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()
    
    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            decimal_places = self.decimal_places_spinbox.value()
            correct_answer = round(self.correct_answer, decimal_places)
            
            if abs(user_answer - correct_answer) < 0.001:  # 允许小的浮点误差
                self.result_label.setText(f"✓ 正确！答案是 {correct_answer}")
                self.result_label.setStyleSheet("QLabel { color: green; }")
            else:
                self.result_label.setText(f"✗ 错误！正确答案是 {correct_answer}")
                self.result_label.setStyleSheet("QLabel { color: red; }")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

def main():
    app = QApplication(sys.argv)
    window = DecimalCalculatorGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
