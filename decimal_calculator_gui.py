import sys
import random
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton,
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox,
                             QFileDialog, QProgressBar, QFrame, QSizePolicy,
                             QSlider, QTabWidget, QTextEdit, QSpacerItem)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon, QPixmap, QPainter, QLinearGradient

class DecimalCalculatorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_a = 0
        self.current_b = 0
        self.current_op = '+'
        self.correct_answer = 0
        self.initUI()
        self.generate_new_problem()
    
    def initUI(self):
        self.setWindowTitle('小数运算练习器')
        self.setGeometry(300, 300, 500, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 设置组
        settings_group = QGroupBox("设置")
        settings_layout = QGridLayout(settings_group)
        
        # 小数位数设置
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places_spinbox = QSpinBox()
        self.decimal_places_spinbox.setRange(1, 3)
        self.decimal_places_spinbox.setValue(1)
        self.decimal_places_spinbox.valueChanged.connect(self.generate_new_problem)
        settings_layout.addWidget(self.decimal_places_spinbox, 0, 1)
        
        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        
        operators_layout = QHBoxLayout()
        self.add_checkbox = QCheckBox('+')
        self.add_checkbox.setChecked(True)
        self.subtract_checkbox = QCheckBox('-')
        self.subtract_checkbox.setChecked(True)
        self.multiply_checkbox = QCheckBox('×')
        self.multiply_checkbox.setChecked(False)
        self.divide_checkbox = QCheckBox('÷')
        self.divide_checkbox.setChecked(False)
        
        operators_layout.addWidget(self.add_checkbox)
        operators_layout.addWidget(self.subtract_checkbox)
        operators_layout.addWidget(self.multiply_checkbox)
        operators_layout.addWidget(self.divide_checkbox)
        
        # 连接复选框信号
        self.add_checkbox.stateChanged.connect(self.generate_new_problem)
        self.subtract_checkbox.stateChanged.connect(self.generate_new_problem)
        self.multiply_checkbox.stateChanged.connect(self.generate_new_problem)
        self.divide_checkbox.stateChanged.connect(self.generate_new_problem)
        
        settings_layout.addLayout(operators_layout, 1, 1)
        
        main_layout.addWidget(settings_group)
        
        # 题目显示区域
        problem_group = QGroupBox("题目")
        problem_layout = QVBoxLayout(problem_group)
        
        # 题目显示
        self.problem_label = QLabel()
        self.problem_label.setFont(QFont("Arial", 24))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 20px; border: 2px solid #ccc; }")
        problem_layout.addWidget(self.problem_label)
        
        # 答案输入
        answer_layout = QHBoxLayout()
        answer_layout.addWidget(QLabel("答案:"))
        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Arial", 16))
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)
        
        problem_layout.addLayout(answer_layout)
        
        main_layout.addWidget(problem_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()

        self.check_button = QPushButton("检查答案")
        self.check_button.clicked.connect(self.check_answer)
        self.check_button.setFont(QFont("Arial", 12))
        button_layout.addWidget(self.check_button)

        self.next_button = QPushButton("下一题")
        self.next_button.clicked.connect(self.generate_new_problem)
        self.next_button.setFont(QFont("Arial", 12))
        button_layout.addWidget(self.next_button)

        main_layout.addLayout(button_layout)

        # Excel生成按钮
        excel_layout = QHBoxLayout()
        self.generate_excel_button = QPushButton("生成1000题Excel文件")
        self.generate_excel_button.clicked.connect(self.generate_excel)
        self.generate_excel_button.setFont(QFont("Arial", 12))
        self.generate_excel_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 10px; }")
        excel_layout.addWidget(self.generate_excel_button)

        main_layout.addLayout(excel_layout)
        
        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Arial", 14))
        self.result_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.result_label)
    
    def generate_decimal_numbers(self):
        """根据设置生成两个小数"""
        decimal_places = self.decimal_places_spinbox.value()
        min_val = 0.1
        max_val = 9.9
        
        # 根据小数位数调整范围
        if decimal_places == 2:
            max_val = 9.99
        elif decimal_places == 3:
            max_val = 9.999
        
        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        return a, b
    
    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_checkbox.isChecked():
            operators.append('+')
        if self.subtract_checkbox.isChecked():
            operators.append('-')
        if self.multiply_checkbox.isChecked():
            operators.append('×')
        if self.divide_checkbox.isChecked():
            operators.append('÷')
        
        if not operators:
            # 如果没有选中任何运算符，默认选择加法
            operators.append('+')
            self.add_checkbox.setChecked(True)
        
        return operators
    
    def ensure_positive_result(self, a, b, op):
        """确保减法结果不为负数，除法结果为整数或简单小数"""
        if op == '-' and a < b:
            return b, a
        elif op == '÷':
            # 确保除法结果是合理的小数
            if b > a:
                a, b = b, a
            # 调整b使得结果是简单的小数
            decimal_places = self.decimal_places_spinbox.value()
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)
        return a, b
    
    def calculate_answer(self, a, b, op):
        """计算正确答案"""
        if op == '+':
            return a + b
        elif op == '-':
            return a - b
        elif op == '×':
            return a * b
        elif op == '÷':
            return a / b
        return 0
    
    def generate_new_problem(self):
        """生成新题目"""
        self.current_a, self.current_b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        self.current_op = random.choice(operators)
        
        self.current_a, self.current_b = self.ensure_positive_result(
            self.current_a, self.current_b, self.current_op)
        
        self.correct_answer = self.calculate_answer(
            self.current_a, self.current_b, self.current_op)
        
        # 显示题目
        problem_text = f"{self.current_a} {self.current_op} {self.current_b} = ?"
        self.problem_label.setText(problem_text)
        
        # 清空输入和结果
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()
    
    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            decimal_places = self.decimal_places_spinbox.value()
            correct_answer = round(self.correct_answer, decimal_places)

            if abs(user_answer - correct_answer) < 0.001:  # 允许小的浮点误差
                self.result_label.setText(f"✓ 正确！答案是 {correct_answer}")
                self.result_label.setStyleSheet("QLabel { color: green; }")
            else:
                self.result_label.setText(f"✗ 错误！正确答案是 {correct_answer}")
                self.result_label.setStyleSheet("QLabel { color: red; }")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

    def generate_problem_data(self):
        """生成单个题目的数据，返回独立的单元格数据"""
        a, b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        op = random.choice(operators)
        a, b = self.ensure_positive_result(a, b, op)
        return a, op, b, '='

    def generate_excel(self):
        """生成1000题Excel文件"""
        try:
            # 生成1000道题目
            problems_data = []
            for i in range(1000):
                problem = self.generate_problem_data()
                problems_data.append(problem)

            # 创建一个包含所有列的字典，每列都有20行
            final_data = {}
            num_rows = 20  # 每列20行

            # 为50组题目创建列
            for group in range(50):
                start_idx = group * 20
                group_problems = problems_data[start_idx:start_idx + 20]

                # 为每组创建4列：第一个数、运算符、第二个数、等号
                col_base = f"第{group+1}组"
                final_data[f'{col_base}_第一个数'] = [p[0] for p in group_problems]
                final_data[f'{col_base}_运算符'] = [p[1] for p in group_problems]
                final_data[f'{col_base}_第二个数'] = [p[2] for p in group_problems]
                final_data[f'{col_base}_等号'] = [p[3] for p in group_problems]

                # 在每组后添加空白列（除了最后一组）
                if group < 49:
                    final_data[f'空白_{group+1}'] = ['' for _ in range(20)]

            # 创建DataFrame
            df = pd.DataFrame(final_data)

            # 选择保存位置
            decimal_places = self.decimal_places_spinbox.value()
            operators = self.get_selected_operators()
            operators_str = ''.join(operators)

            default_filename = f'小数运算练习_{decimal_places}位小数_{operators_str}_1000题.xlsx'
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存Excel文件",
                default_filename,
                "Excel files (*.xlsx);;All files (*.*)"
            )

            if filename:
                # 保存到Excel文件
                df.to_excel(filename, index=False)

                QMessageBox.information(
                    self,
                    "生成成功",
                    f"已成功生成1000道题目！\n"
                    f"文件保存为：{filename}\n"
                    f"小数位数：{decimal_places}位\n"
                    f"运算符：{', '.join(operators)}\n"
                    f"题目按每20题一组分为50列组合\n"
                    f"DataFrame形状: {df.shape}"
                )

        except Exception as e:
            QMessageBox.critical(self, "生成失败", f"生成Excel文件时出错：{str(e)}")

def main():
    app = QApplication(sys.argv)
    window = DecimalCalculatorGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
