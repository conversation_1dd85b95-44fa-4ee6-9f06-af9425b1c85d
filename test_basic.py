import random

def generate_problem():
    """生成一个小数运算题目"""
    a = round(random.uniform(0.1, 9.9), 1)
    b = round(random.uniform(0.1, 9.9), 1)
    op = random.choice(['+', '-'])
    
    # 确保减法结果为正
    if op == '-' and a < b:
        a, b = b, a
    
    # 计算结果
    if op == '+':
        result = round(a + b, 1)
    else:
        result = round(a - b, 1)
    
    return a, op, b, '=', result

print("🧮 小数运算题目生成测试")
print("=" * 40)

print("\n📝 生成10个示例题目：")
for i in range(10):
    a, op, b, eq, result = generate_problem()
    print(f"{i+1:2d}. {a} {op} {b} {eq} {result}")

print("\n✅ 基本功能测试完成！")
print("\n📊 Excel格式说明：")
print("每个题目将占用5个单元格：")
print("| 数字A | 运算符 | 数字B | 等号 | 结果 |")
print("| 3.2   | +     | 1.5   | =   | 4.7  |")
print("| 5.8   | -     | 2.3   | =   | 3.5  |")

print("\n🔧 每组20题，共50组，总计1000题")
print("📋 组与组之间用空白列分隔")
