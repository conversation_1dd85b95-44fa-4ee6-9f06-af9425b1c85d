import tkinter as tk
from tkinter import ttk, messagebox
import random
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class TkinterCalculator:
    def __init__(self):
        self.root = tk.Tk()
        self.correct_answer = 0
        self.setup_ui()
        self.generate_new_problem()
    
    def setup_ui(self):
        self.root.title('🧮 小数运算练习器 - 界面优化版')
        self.root.geometry('1000x900')
        self.root.configure(bg='#667eea')
        
        # 创建主框架
        main_frame = tk.Frame(self.root, bg='#667eea', padx=40, pady=40)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text='🧮 小数运算练习器',
            font=('Microsoft YaHei', 24, 'bold'),
            fg='white',
            bg='#667eea',
            pady=20
        )
        title_label.pack(fill='x')
        
        # 设置框架
        settings_frame = tk.LabelFrame(
            main_frame,
            text='⚙️ 游戏设置',
            font=('Microsoft YaHei', 16, 'bold'),
            fg='#1976D2',
            bg='white',
            padx=20,
            pady=20
        )
        settings_frame.pack(fill='x', pady=10)
        
        # 小数位数设置
        decimal_frame = tk.Frame(settings_frame, bg='white')
        decimal_frame.pack(fill='x', pady=10)
        
        tk.Label(
            decimal_frame,
            text='小数位数:',
            font=('Microsoft YaHei', 14, 'bold'),
            bg='white'
        ).pack(side='left')
        
        self.decimal_var = tk.IntVar(value=1)
        decimal_spinbox = tk.Spinbox(
            decimal_frame,
            from_=1,
            to=3,
            textvariable=self.decimal_var,
            font=('Microsoft YaHei', 14),
            width=10,
            command=self.generate_new_problem
        )
        decimal_spinbox.pack(side='left', padx=10)
        
        # 运算符选择
        operator_frame = tk.Frame(settings_frame, bg='white')
        operator_frame.pack(fill='x', pady=10)
        
        tk.Label(
            operator_frame,
            text='运算符:',
            font=('Microsoft YaHei', 14, 'bold'),
            bg='white'
        ).pack(side='left')
        
        self.add_var = tk.BooleanVar(value=True)
        self.subtract_var = tk.BooleanVar(value=True)
        self.multiply_var = tk.BooleanVar(value=False)
        self.divide_var = tk.BooleanVar(value=False)
        
        operators = [
            ('➕ 加法', self.add_var),
            ('➖ 减法', self.subtract_var),
            ('✖️ 乘法', self.multiply_var),
            ('➗ 除法', self.divide_var)
        ]
        
        for text, var in operators:
            cb = tk.Checkbutton(
                operator_frame,
                text=text,
                variable=var,
                font=('Microsoft YaHei', 12),
                bg='white',
                command=self.generate_new_problem
            )
            cb.pack(side='left', padx=10)
        
        # 题目显示框架
        problem_frame = tk.LabelFrame(
            main_frame,
            text='📝 当前题目',
            font=('Microsoft YaHei', 16, 'bold'),
            fg='#388E3C',
            bg='white',
            padx=20,
            pady=20
        )
        problem_frame.pack(fill='x', pady=10)
        
        # 题目显示
        self.problem_label = tk.Label(
            problem_frame,
            text='',
            font=('Microsoft YaHei', 32, 'bold'),
            fg='#2E7D32',
            bg='#E8F5E8',
            pady=30,
            relief='solid',
            borderwidth=3
        )
        self.problem_label.pack(fill='x', pady=10)
        
        # 答案输入框架
        answer_frame = tk.Frame(problem_frame, bg='white')
        answer_frame.pack(fill='x', pady=10)
        
        tk.Label(
            answer_frame,
            text='💡 你的答案:',
            font=('Microsoft YaHei', 16, 'bold'),
            bg='white'
        ).pack(side='left')
        
        self.answer_var = tk.StringVar()
        self.answer_entry = tk.Entry(
            answer_frame,
            textvariable=self.answer_var,
            font=('Microsoft YaHei', 20),
            width=15,
            justify='center'
        )
        self.answer_entry.pack(side='left', padx=10, fill='x', expand=True)
        self.answer_entry.bind('<Return>', lambda e: self.check_answer())
        
        # 按钮框架
        button_frame = tk.LabelFrame(
            main_frame,
            text='🎮 操作按钮',
            font=('Microsoft YaHei', 16, 'bold'),
            fg='#FF6F00',
            bg='white',
            padx=20,
            pady=20
        )
        button_frame.pack(fill='x', pady=10)
        
        buttons_container = tk.Frame(button_frame, bg='white')
        buttons_container.pack(fill='x')
        
        # 检查答案按钮
        check_button = tk.Button(
            buttons_container,
            text='✅ 检查答案',
            font=('Microsoft YaHei', 16, 'bold'),
            bg='#4CAF50',
            fg='white',
            padx=20,
            pady=10,
            command=self.check_answer,
            relief='flat'
        )
        check_button.pack(side='left', padx=10, fill='x', expand=True)
        
        # 下一题按钮
        next_button = tk.Button(
            buttons_container,
            text='🔄 下一题',
            font=('Microsoft YaHei', 16, 'bold'),
            bg='#2196F3',
            fg='white',
            padx=20,
            pady=10,
            command=self.generate_new_problem,
            relief='flat'
        )
        next_button.pack(side='left', padx=10, fill='x', expand=True)
        
        # Excel生成框架
        if PANDAS_AVAILABLE:
            excel_frame = tk.LabelFrame(
                main_frame,
                text='📊 Excel生成',
                font=('Microsoft YaHei', 16, 'bold'),
                fg='#388E3C',
                bg='white',
                padx=20,
                pady=20
            )
            excel_frame.pack(fill='x', pady=10)
            
            excel_button = tk.Button(
                excel_frame,
                text='📊 生成1000题Excel文件',
                font=('Microsoft YaHei', 16, 'bold'),
                bg='#FF9800',
                fg='white',
                padx=20,
                pady=15,
                command=self.generate_excel,
                relief='flat'
            )
            excel_button.pack(fill='x')
        
        # 结果显示框架
        result_frame = tk.LabelFrame(
            main_frame,
            text='📋 答题结果',
            font=('Microsoft YaHei', 16, 'bold'),
            fg='#7B1FA2',
            bg='white',
            padx=20,
            pady=20
        )
        result_frame.pack(fill='x', pady=10)
        
        self.result_label = tk.Label(
            result_frame,
            text="点击'下一题'开始练习",
            font=('Microsoft YaHei', 16, 'bold'),
            fg='#424242',
            bg='white',
            pady=20,
            relief='solid',
            borderwidth=2
        )
        self.result_label.pack(fill='x', pady=10)
    
    def generate_decimal_numbers(self):
        """根据设置生成两个小数"""
        decimal_places = self.decimal_var.get()
        min_val = 0.1
        max_val = 9.9
        
        # 根据小数位数调整范围
        if decimal_places == 2:
            max_val = 9.99
        elif decimal_places == 3:
            max_val = 9.999
        
        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        
        return a, b
    
    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_var.get():
            operators.append('+')
        if self.subtract_var.get():
            operators.append('-')
        if self.multiply_var.get():
            operators.append('×')
        if self.divide_var.get():
            operators.append('÷')
        
        # 如果没有选择任何运算符，默认使用加法
        if not operators:
            operators = ['+']
            self.add_var.set(True)
        
        return operators
    
    def generate_new_problem(self):
        """生成新题目"""
        a, b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        operator = random.choice(operators)
        
        # 确保减法结果为正数
        if operator == '-' and a < b:
            a, b = b, a
        
        # 确保除法结果合理
        if operator == '÷':
            # 确保除数不为0且结果不会太复杂
            if b == 0:
                b = 0.1
            # 如果被除数小于除数，交换位置
            if a < b:
                a, b = b, a
        
        # 计算正确答案
        if operator == '+':
            self.correct_answer = round(a + b, self.decimal_var.get())
        elif operator == '-':
            self.correct_answer = round(a - b, self.decimal_var.get())
        elif operator == '×':
            self.correct_answer = round(a * b, self.decimal_var.get())
        elif operator == '÷':
            self.correct_answer = round(a / b, self.decimal_var.get())
        
        # 显示题目
        self.problem_label.config(text=f"{a} {operator} {b} = ?")
        
        # 清空输入框和结果
        self.answer_var.set('')
        self.result_label.config(text="请输入答案", bg='white', fg='#424242')
        
        # 设置焦点到输入框
        self.answer_entry.focus_set()
    
    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_var.get())
            
            if abs(user_answer - self.correct_answer) < 0.001:
                # 答对了
                self.result_label.config(
                    text=f"🎉 正确！答案是 {self.correct_answer}",
                    bg='#E8F5E8',
                    fg='#2E7D32'
                )
            else:
                # 答错了
                self.result_label.config(
                    text=f"❌ 错误！正确答案是 {self.correct_answer}",
                    bg='#FFEBEE',
                    fg='#C62828'
                )
                
        except ValueError:
            messagebox.showwarning("输入错误", "请输入有效的数字！")
    
    def generate_problem_data(self):
        """生成单个题目数据（用于Excel）"""
        a, b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        operator = random.choice(operators)
        
        # 确保减法结果为正数
        if operator == '-' and a < b:
            a, b = b, a
        
        # 确保除法结果合理
        if operator == '÷':
            if b == 0:
                b = 0.1
            if a < b:
                a, b = b, a
        
        # 计算结果
        if operator == '+':
            result = round(a + b, self.decimal_var.get())
        elif operator == '-':
            result = round(a - b, self.decimal_var.get())
        elif operator == '×':
            result = round(a * b, self.decimal_var.get())
        elif operator == '÷':
            result = round(a / b, self.decimal_var.get())
        
        return [a, operator, b, '=', result]

    def generate_excel(self):
        """生成Excel文件"""
        if not PANDAS_AVAILABLE:
            messagebox.showwarning("缺少依赖", "需要安装pandas库才能生成Excel文件！\n请运行: pip install pandas openpyxl")
            return

        try:
            # 生成1000道题目
            problems_data = []
            for i in range(1000):
                problem = self.generate_problem_data()
                problems_data.append(problem)

            # 创建一个包含所有列的字典，每列都有20行
            final_data = {}
            num_rows = 20  # 每列20行

            # 为50组题目创建列
            for group in range(50):
                start_idx = group * num_rows
                end_idx = start_idx + num_rows
                group_problems = problems_data[start_idx:end_idx]

                # 为每组创建5列（数字A, 运算符, 数字B, 等号, 结果）
                group_name = f"第{group+1}组"
                final_data[f"{group_name}_数字A"] = [problem[0] for problem in group_problems]
                final_data[f"{group_name}_运算符"] = [problem[1] for problem in group_problems]
                final_data[f"{group_name}_数字B"] = [problem[2] for problem in group_problems]
                final_data[f"{group_name}_等号"] = [problem[3] for problem in group_problems]
                final_data[f"{group_name}_结果"] = [problem[4] for problem in group_problems]

            # 创建DataFrame并保存
            df = pd.DataFrame(final_data)

            decimal_places = self.decimal_var.get()
            filename = f'小数运算练习_{decimal_places}位小数_1000题.xlsx'
            df.to_excel(filename, index=False)

            messagebox.showinfo(
                "生成成功",
                f"✅ Excel文件生成成功！\n\n"
                f"📁 文件名: {filename}\n"
                f"📊 格式: 每个题目占用5个单元格\n"
                f"📋 结构: 数字A | 运算符 | 数字B | 等号 | 结果\n"
                f"📈 总计: 1000题，分50组，每组20题\n\n"
                f"🎉 文件已保存到当前目录！"
            )

        except Exception as e:
            messagebox.showerror("生成失败", f"生成Excel文件时出错：\n{str(e)}")

    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    app = TkinterCalculator()
    app.run()

if __name__ == '__main__':
    main()
