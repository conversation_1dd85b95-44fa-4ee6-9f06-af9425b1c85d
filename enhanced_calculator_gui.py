import sys
import random
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton, 
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox,
                             QFileDialog, QProgressBar, QFrame, QTabWidget, 
                             QTextEdit, QSpacerItem, QSlider)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor, QLinearGradient, QPainter

class ModernButton(QPushButton):
    """现代化按钮样式"""
    def __init__(self, text, color_scheme="blue"):
        super().__init__(text)
        self.color_scheme = color_scheme
        self.setStyleSheet(self.get_style())
        self.setMinimumHeight(45)
        self.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
    
    def get_style(self):
        if self.color_scheme == "blue":
            return """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4A90E2, stop:1 #357ABD);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5BA0F2, stop:1 #4A8ACD);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3A80D2, stop:1 #2A6AAD);
                }
            """
        elif self.color_scheme == "green":
            return """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #45A049);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5CBF60, stop:1 #55B059);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3C9F40, stop:1 #359039);
                }
            """
        elif self.color_scheme == "orange":
            return """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #FF9800, stop:1 #F57C00);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    padding: 10px 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #FFA726, stop:1 #FB8C00);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #EF6C00, stop:1 #E65100);
                }
            """

class ModernGroupBox(QGroupBox):
    """现代化分组框样式"""
    def __init__(self, title):
        super().__init__(title)
        self.setStyleSheet("""
            QGroupBox {
                font: bold 12px "Microsoft YaHei";
                border: 2px solid #E0E0E0;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #FAFAFA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #FAFAFA;
            }
        """)

class EnhancedDecimalCalculator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_a = 0
        self.current_b = 0
        self.current_op = '+'
        self.correct_answer = 0
        self.score = 0
        self.total_questions = 0
        self.streak = 0
        self.max_streak = 0
        
        # 定时器用于动画效果
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animations)
        
        self.initUI()
        self.apply_modern_style()
        self.generate_new_problem()
    
    def initUI(self):
        self.setWindowTitle('🧮 智能小数运算练习器 Pro')
        self.setGeometry(200, 100, 800, 700)
        self.setMinimumSize(700, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(self.tab_widget)
        
        # 练习标签页
        self.practice_tab = QWidget()
        self.tab_widget.addTab(self.practice_tab, "🎯 练习模式")
        
        # Excel生成标签页
        self.excel_tab = QWidget()
        self.tab_widget.addTab(self.excel_tab, "📊 批量生成")
        
        # 统计标签页
        self.stats_tab = QWidget()
        self.tab_widget.addTab(self.stats_tab, "📈 学习统计")
        
        self.setup_practice_tab()
        self.setup_excel_tab()
        self.setup_stats_tab()
    
    def setup_practice_tab(self):
        """设置练习标签页"""
        layout = QVBoxLayout(self.practice_tab)
        
        # 顶部状态栏
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        status_layout = QHBoxLayout(status_frame)
        
        self.score_label = QLabel("得分: 0")
        self.score_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        
        self.streak_label = QLabel("连击: 0")
        self.streak_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        
        self.accuracy_label = QLabel("正确率: 0%")
        self.accuracy_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        
        status_layout.addWidget(self.score_label)
        status_layout.addWidget(self.streak_label)
        status_layout.addWidget(self.accuracy_label)
        status_layout.addStretch()
        
        layout.addWidget(status_frame)
        
        # 设置区域
        settings_group = ModernGroupBox("⚙️ 设置选项")
        settings_layout = QGridLayout(settings_group)
        
        # 小数位数设置
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places_spinbox = QSpinBox()
        self.decimal_places_spinbox.setRange(1, 3)
        self.decimal_places_spinbox.setValue(1)
        self.decimal_places_spinbox.setStyleSheet(self.get_spinbox_style())
        self.decimal_places_spinbox.valueChanged.connect(self.generate_new_problem)
        settings_layout.addWidget(self.decimal_places_spinbox, 0, 1)
        
        # 难度滑块
        settings_layout.addWidget(QLabel("数值范围:"), 0, 2)
        self.range_slider = QSlider(Qt.Horizontal)
        self.range_slider.setRange(1, 5)
        self.range_slider.setValue(3)
        self.range_slider.setStyleSheet(self.get_slider_style())
        self.range_slider.valueChanged.connect(self.generate_new_problem)
        settings_layout.addWidget(self.range_slider, 0, 3)
        
        self.range_label = QLabel("中等 (0.1-9.9)")
        self.range_label.setStyleSheet("color: #666; font-size: 10px;")
        settings_layout.addWidget(self.range_label, 1, 3)
        
        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        
        operators_layout = QHBoxLayout()
        self.add_checkbox = QCheckBox('➕ 加法')
        self.add_checkbox.setChecked(True)
        self.subtract_checkbox = QCheckBox('➖ 减法')
        self.subtract_checkbox.setChecked(True)
        self.multiply_checkbox = QCheckBox('✖️ 乘法')
        self.multiply_checkbox.setChecked(False)
        self.divide_checkbox = QCheckBox('➗ 除法')
        self.divide_checkbox.setChecked(False)
        
        for checkbox in [self.add_checkbox, self.subtract_checkbox, 
                        self.multiply_checkbox, self.divide_checkbox]:
            checkbox.setStyleSheet(self.get_checkbox_style())
            checkbox.stateChanged.connect(self.generate_new_problem)
            operators_layout.addWidget(checkbox)
        
        settings_layout.addLayout(operators_layout, 1, 1, 1, 3)
        
        layout.addWidget(settings_group)
        
        # 题目显示区域
        problem_group = ModernGroupBox("📝 当前题目")
        problem_layout = QVBoxLayout(problem_group)
        
        # 题目显示
        self.problem_label = QLabel()
        self.problem_label.setFont(QFont("Microsoft YaHei", 32, QFont.Bold))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f093fb, stop:1 #f5576c);
                color: white;
                padding: 30px;
                border-radius: 15px;
                margin: 10px;
            }
        """)
        problem_layout.addWidget(self.problem_label)
        
        # 答案输入
        answer_layout = QHBoxLayout()
        answer_label = QLabel("💡 答案:")
        answer_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        answer_layout.addWidget(answer_label)
        
        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Microsoft YaHei", 18))
        self.answer_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 12px;
                font-size: 18px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #4A90E2;
                background-color: #F8F9FA;
            }
        """)
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)
        
        problem_layout.addLayout(answer_layout)
        layout.addWidget(problem_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.check_button = ModernButton("✅ 检查答案", "blue")
        self.check_button.clicked.connect(self.check_answer)
        button_layout.addWidget(self.check_button)
        
        self.next_button = ModernButton("⏭️ 下一题", "green")
        self.next_button.clicked.connect(self.generate_new_problem)
        button_layout.addWidget(self.next_button)
        
        self.hint_button = ModernButton("💡 提示", "orange")
        self.hint_button.clicked.connect(self.show_hint)
        button_layout.addWidget(self.hint_button)
        
        layout.addLayout(button_layout)
        
        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumHeight(50)
        layout.addWidget(self.result_label)

    def setup_excel_tab(self):
        """设置Excel生成标签页"""
        layout = QVBoxLayout(self.excel_tab)

        # 标题
        title_label = QLabel("📊 批量生成Excel练习题")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #333; margin: 20px;")
        layout.addWidget(title_label)

        # 生成设置
        excel_settings_group = ModernGroupBox("🔧 生成设置")
        excel_settings_layout = QGridLayout(excel_settings_group)

        # 题目数量
        excel_settings_layout.addWidget(QLabel("题目总数:"), 0, 0)
        self.total_problems_spinbox = QSpinBox()
        self.total_problems_spinbox.setRange(100, 5000)
        self.total_problems_spinbox.setValue(1000)
        self.total_problems_spinbox.setSuffix(" 题")
        self.total_problems_spinbox.setStyleSheet(self.get_spinbox_style())
        excel_settings_layout.addWidget(self.total_problems_spinbox, 0, 1)

        # 每组题目数
        excel_settings_layout.addWidget(QLabel("每组题数:"), 0, 2)
        self.group_size_spinbox = QSpinBox()
        self.group_size_spinbox.setRange(10, 50)
        self.group_size_spinbox.setValue(20)
        self.group_size_spinbox.setSuffix(" 题/组")
        self.group_size_spinbox.setStyleSheet(self.get_spinbox_style())
        excel_settings_layout.addWidget(self.group_size_spinbox, 0, 3)

        # Excel小数位数
        excel_settings_layout.addWidget(QLabel("小数位数:"), 1, 0)
        self.excel_decimal_places_spinbox = QSpinBox()
        self.excel_decimal_places_spinbox.setRange(1, 3)
        self.excel_decimal_places_spinbox.setValue(1)
        self.excel_decimal_places_spinbox.setStyleSheet(self.get_spinbox_style())
        excel_settings_layout.addWidget(self.excel_decimal_places_spinbox, 1, 1)

        # Excel运算符选择
        excel_settings_layout.addWidget(QLabel("运算符:"), 1, 2)
        excel_operators_layout = QHBoxLayout()

        self.excel_add_checkbox = QCheckBox('➕')
        self.excel_add_checkbox.setChecked(True)
        self.excel_subtract_checkbox = QCheckBox('➖')
        self.excel_subtract_checkbox.setChecked(True)
        self.excel_multiply_checkbox = QCheckBox('✖️')
        self.excel_divide_checkbox = QCheckBox('➗')

        for checkbox in [self.excel_add_checkbox, self.excel_subtract_checkbox,
                        self.excel_multiply_checkbox, self.excel_divide_checkbox]:
            checkbox.setStyleSheet(self.get_checkbox_style())
            excel_operators_layout.addWidget(checkbox)

        excel_settings_layout.addLayout(excel_operators_layout, 1, 3)

        layout.addWidget(excel_settings_group)

        # 预览区域
        preview_group = ModernGroupBox("👀 生成预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                background-color: #F8F9FA;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        preview_layout.addWidget(self.preview_text)

        preview_button = ModernButton("🔍 预览前10题", "blue")
        preview_button.clicked.connect(self.preview_excel_problems)
        preview_layout.addWidget(preview_button)

        layout.addWidget(preview_group)

        # 生成按钮
        generate_layout = QHBoxLayout()
        generate_layout.addStretch()

        self.generate_excel_button = ModernButton("📊 生成Excel文件", "green")
        self.generate_excel_button.setMinimumHeight(60)
        self.generate_excel_button.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.generate_excel_button.clicked.connect(self.generate_excel)
        generate_layout.addWidget(self.generate_excel_button)

        generate_layout.addStretch()
        layout.addLayout(generate_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)

        layout.addStretch()

    def setup_stats_tab(self):
        """设置统计标签页"""
        layout = QVBoxLayout(self.stats_tab)

        # 标题
        title_label = QLabel("📈 学习统计分析")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #333; margin: 20px;")
        layout.addWidget(title_label)

        # 统计卡片
        stats_layout = QGridLayout()

        # 总题数卡片
        total_card = self.create_stat_card("📝", "总答题数", "0", "#4A90E2")
        stats_layout.addWidget(total_card, 0, 0)

        # 正确率卡片
        accuracy_card = self.create_stat_card("🎯", "正确率", "0%", "#4CAF50")
        stats_layout.addWidget(accuracy_card, 0, 1)

        # 最高连击卡片
        streak_card = self.create_stat_card("🔥", "最高连击", "0", "#FF9800")
        stats_layout.addWidget(streak_card, 0, 2)

        # 平均用时卡片
        time_card = self.create_stat_card("⏱️", "平均用时", "0s", "#9C27B0")
        stats_layout.addWidget(time_card, 1, 0)

        # 今日练习卡片
        today_card = self.create_stat_card("📅", "今日练习", "0题", "#F44336")
        stats_layout.addWidget(today_card, 1, 1)

        # 学习等级卡片
        level_card = self.create_stat_card("🏆", "学习等级", "初学者", "#607D8B")
        stats_layout.addWidget(level_card, 1, 2)

        layout.addLayout(stats_layout)

        # 重置按钮
        reset_layout = QHBoxLayout()
        reset_layout.addStretch()

        reset_button = ModernButton("🔄 重置统计", "orange")
        reset_button.clicked.connect(self.reset_stats)
        reset_layout.addWidget(reset_button)

        reset_layout.addStretch()
        layout.addLayout(reset_layout)

        layout.addStretch()

    def create_stat_card(self, icon, title, value, color):
        """创建统计卡片"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
            }}
            QFrame:hover {{
                border: 2px solid {color};
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
        """)

        layout = QVBoxLayout(card)

        # 图标和标题
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI Emoji", 24))
        header_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setStyleSheet("color: #666;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 数值
        value_label = QLabel(value)
        value_label.setFont(QFont("Microsoft YaHei", 20, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        return card

    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #F5F5F5;
            }
            QTabWidget::pane {
                border: 1px solid #E0E0E0;
                border-radius: 5px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                background-color: #F0F0F0;
                border: 1px solid #E0E0E0;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font: bold 12px "Microsoft YaHei";
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
                color: #4A90E2;
            }
            QTabBar::tab:hover {
                background-color: #E8F4FD;
            }
        """)

    def get_spinbox_style(self):
        """获取数字选择框样式"""
        return """
            QSpinBox {
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
                min-width: 80px;
            }
            QSpinBox:focus {
                border: 2px solid #4A90E2;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border: none;
                background-color: #F0F0F0;
                border-radius: 3px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #4A90E2;
            }
        """

    def get_slider_style(self):
        """获取滑块样式"""
        return """
            QSlider::groove:horizontal {
                border: 1px solid #E0E0E0;
                height: 8px;
                background: #F0F0F0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #4A90E2;
                border: 2px solid #357ABD;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #5BA0F2;
            }
        """

    def get_checkbox_style(self):
        """获取复选框样式"""
        return """
            QCheckBox {
                font: 11px "Microsoft YaHei";
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #E0E0E0;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4A90E2;
                border: 2px solid #357ABD;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #4A90E2;
            }
        """

    def generate_decimal_numbers(self):
        """根据设置生成两个小数"""
        decimal_places = self.decimal_places_spinbox.value()
        range_value = self.range_slider.value()

        # 根据滑块值调整范围
        ranges = {
            1: (0.1, 2.9),    # 简单
            2: (0.1, 4.9),    # 较简单
            3: (0.1, 9.9),    # 中等
            4: (0.1, 19.9),   # 较难
            5: (0.1, 99.9)    # 困难
        }

        min_val, max_val = ranges[range_value]

        # 更新范围标签
        range_labels = {
            1: "简单 (0.1-2.9)",
            2: "较简单 (0.1-4.9)",
            3: "中等 (0.1-9.9)",
            4: "较难 (0.1-19.9)",
            5: "困难 (0.1-99.9)"
        }
        self.range_label.setText(range_labels[range_value])

        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        return a, b

    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_checkbox.isChecked():
            operators.append('+')
        if self.subtract_checkbox.isChecked():
            operators.append('-')
        if self.multiply_checkbox.isChecked():
            operators.append('×')
        if self.divide_checkbox.isChecked():
            operators.append('÷')

        if not operators:
            operators.append('+')
            self.add_checkbox.setChecked(True)

        return operators

    def ensure_positive_result(self, a, b, op):
        """确保减法结果不为负数，除法结果为整数或简单小数"""
        if op == '-' and a < b:
            return b, a
        elif op == '÷':
            if b > a:
                a, b = b, a
            decimal_places = self.decimal_places_spinbox.value()
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)
        return a, b

    def calculate_answer(self, a, b, op):
        """计算正确答案"""
        if op == '+':
            return a + b
        elif op == '-':
            return a - b
        elif op == '×':
            return a * b
        elif op == '÷':
            return a / b
        return 0

    def generate_new_problem(self):
        """生成新题目"""
        self.current_a, self.current_b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        self.current_op = random.choice(operators)

        self.current_a, self.current_b = self.ensure_positive_result(
            self.current_a, self.current_b, self.current_op)

        self.correct_answer = self.calculate_answer(
            self.current_a, self.current_b, self.current_op)

        # 显示题目
        problem_text = f"{self.current_a} {self.current_op} {self.current_b} = ?"
        self.problem_label.setText(problem_text)

        # 清空输入和结果
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()

    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            decimal_places = self.decimal_places_spinbox.value()
            correct_answer = round(self.correct_answer, decimal_places)

            self.total_questions += 1

            if abs(user_answer - correct_answer) < 0.001:
                self.score += 1
                self.streak += 1
                self.max_streak = max(self.max_streak, self.streak)

                self.result_label.setText(f"🎉 正确！答案是 {correct_answer}")
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        background-color: #E8F5E8;
                        border: 2px solid #4CAF50;
                        border-radius: 8px;
                        padding: 10px;
                    }
                """)

                # 播放成功动画效果
                self.animate_success()

            else:
                self.streak = 0
                self.result_label.setText(f"❌ 错误！正确答案是 {correct_answer}")
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #F44336;
                        background-color: #FFEBEE;
                        border: 2px solid #F44336;
                        border-radius: 8px;
                        padding: 10px;
                    }
                """)

            self.update_status_bar()

        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

    def show_hint(self):
        """显示提示"""
        if self.current_op == '+':
            hint = f"提示：{self.current_a} + {self.current_b} = ?"
        elif self.current_op == '-':
            hint = f"提示：{self.current_a} - {self.current_b} = ?"
        elif self.current_op == '×':
            hint = f"提示：{self.current_a} × {self.current_b} = ?"
        elif self.current_op == '÷':
            hint = f"提示：{self.current_a} ÷ {self.current_b} = ?"

        decimal_places = self.decimal_places_spinbox.value()
        correct_answer = round(self.correct_answer, decimal_places)

        # 显示部分答案作为提示
        answer_str = str(correct_answer)
        if len(answer_str) > 1:
            hint_answer = answer_str[0] + "?" * (len(answer_str) - 1)
            hint += f"\n答案开头是：{hint_answer}"

        QMessageBox.information(self, "💡 提示", hint)

    def update_status_bar(self):
        """更新状态栏"""
        accuracy = (self.score / self.total_questions * 100) if self.total_questions > 0 else 0

        self.score_label.setText(f"得分: {self.score}")
        self.streak_label.setText(f"连击: {self.streak}")
        self.accuracy_label.setText(f"正确率: {accuracy:.1f}%")

    def animate_success(self):
        """成功动画效果"""
        # 这里可以添加更复杂的动画效果
        pass

    def update_animations(self):
        """更新动画"""
        pass

    def get_excel_selected_operators(self):
        """获取Excel生成选中的运算符"""
        operators = []
        if self.excel_add_checkbox.isChecked():
            operators.append('+')
        if self.excel_subtract_checkbox.isChecked():
            operators.append('-')
        if self.excel_multiply_checkbox.isChecked():
            operators.append('×')
        if self.excel_divide_checkbox.isChecked():
            operators.append('÷')

        if not operators:
            operators.append('+')
            self.excel_add_checkbox.setChecked(True)

        return operators

    def generate_problem_data_for_excel(self, decimal_places, operators):
        """为Excel生成单个题目的数据"""
        min_val = 0.1
        max_val = 9.9

        if decimal_places == 2:
            max_val = 9.99
        elif decimal_places == 3:
            max_val = 9.999

        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        op = random.choice(operators)

        # 确保结果合理
        if op == '-' and a < b:
            a, b = b, a
        elif op == '÷':
            if b > a:
                a, b = b, a
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)

        return a, op, b, '='

    def preview_excel_problems(self):
        """预览Excel题目"""
        decimal_places = self.excel_decimal_places_spinbox.value()
        operators = self.get_excel_selected_operators()

        preview_text = "前10题预览：\n\n"
        for i in range(10):
            a, op, b, eq = self.generate_problem_data_for_excel(decimal_places, operators)
            preview_text += f"{i+1:2d}. {a} {op} {b} {eq}\n"

        preview_text += f"\n设置：{decimal_places}位小数，运算符：{', '.join(operators)}"
        self.preview_text.setText(preview_text)

    def generate_excel(self):
        """生成Excel文件"""
        try:
            total_problems = self.total_problems_spinbox.value()
            group_size = self.group_size_spinbox.value()
            decimal_places = self.excel_decimal_places_spinbox.value()
            operators = self.get_excel_selected_operators()

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # 生成题目
            problems_data = []
            for i in range(total_problems):
                problem = self.generate_problem_data_for_excel(decimal_places, operators)
                problems_data.append(problem)

                # 更新进度
                progress = int((i + 1) / total_problems * 50)
                self.progress_bar.setValue(progress)
                QApplication.processEvents()

            # 创建DataFrame
            final_data = {}
            num_groups = (total_problems + group_size - 1) // group_size

            for group in range(num_groups):
                start_idx = group * group_size
                end_idx = min(start_idx + group_size, total_problems)
                group_problems = problems_data[start_idx:end_idx]

                # 补齐不足的题目
                while len(group_problems) < group_size:
                    group_problems.append(['', '', '', ''])

                # 为每组创建4列
                col_base = f"第{group+1}组"
                final_data[f'{col_base}_第一个数'] = [p[0] for p in group_problems]
                final_data[f'{col_base}_运算符'] = [p[1] for p in group_problems]
                final_data[f'{col_base}_第二个数'] = [p[2] for p in group_problems]
                final_data[f'{col_base}_等号'] = [p[3] for p in group_problems]

                # 添加空白列
                if group < num_groups - 1:
                    final_data[f'空白_{group+1}'] = ['' for _ in range(group_size)]

                # 更新进度
                progress = 50 + int((group + 1) / num_groups * 40)
                self.progress_bar.setValue(progress)
                QApplication.processEvents()

            # 创建DataFrame
            df = pd.DataFrame(final_data)

            # 选择保存位置
            operators_str = ''.join(operators)
            default_filename = f'小数运算练习_{decimal_places}位小数_{operators_str}_{total_problems}题.xlsx'

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存Excel文件",
                default_filename,
                "Excel files (*.xlsx);;All files (*.*)"
            )

            if filename:
                df.to_excel(filename, index=False)
                self.progress_bar.setValue(100)

                QMessageBox.information(
                    self,
                    "🎉 生成成功",
                    f"已成功生成{total_problems}道题目！\n\n"
                    f"📁 文件：{filename}\n"
                    f"🔢 小数位数：{decimal_places}位\n"
                    f"➕ 运算符：{', '.join(operators)}\n"
                    f"📊 分组：每{group_size}题一组，共{num_groups}组\n"
                    f"📈 表格大小：{df.shape[0]}行 × {df.shape[1]}列"
                )

            self.progress_bar.setVisible(False)

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "❌ 生成失败", f"生成Excel文件时出错：\n{str(e)}")

    def reset_stats(self):
        """重置统计"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要重置所有统计数据吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.score = 0
            self.total_questions = 0
            self.streak = 0
            self.max_streak = 0
            self.update_status_bar()
            QMessageBox.information(self, "✅ 重置完成", "统计数据已重置！")

def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 设置应用程序图标和信息
    app.setApplicationName("智能小数运算练习器 Pro")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("数学练习工具")

    window = EnhancedDecimalCalculator()
    window.show()

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
