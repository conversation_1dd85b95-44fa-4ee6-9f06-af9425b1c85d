import sys
import random
import time
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton,
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox,
                             QProgressBar, QSlider, QFrame, QStackedWidget)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor

class GameStats:
    """游戏统计系统"""
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.score = 0
        self.total_questions = 0
        self.correct_answers = 0
        self.streak = 0
        self.max_streak = 0
        self.total_time = 0
        self.best_time = float('inf')
        self.level = 1
        self.exp = 0
        self.exp_to_next = 100
    
    def add_correct_answer(self, answer_time, difficulty):
        """添加正确答案"""
        self.correct_answers += 1
        self.total_questions += 1
        self.streak += 1
        self.max_streak = max(self.max_streak, self.streak)
        self.total_time += answer_time
        self.best_time = min(self.best_time, answer_time)
        
        # 计算得分
        base_score = 10
        difficulty_bonus = difficulty * 5
        speed_bonus = max(0, int((5 - answer_time) * 2))
        streak_bonus = min(self.streak * 2, 50)
        
        score_gained = base_score + difficulty_bonus + speed_bonus + streak_bonus
        self.score += score_gained
        
        # 计算经验值
        exp_gained = 5 + difficulty * 2 + max(0, int((3 - answer_time) * 3))
        self.add_exp(exp_gained)
        
        return score_gained, exp_gained
    
    def add_wrong_answer(self):
        """添加错误答案"""
        self.total_questions += 1
        self.streak = 0
    
    def add_exp(self, exp):
        """添加经验值"""
        self.exp += exp
        while self.exp >= self.exp_to_next:
            self.exp -= self.exp_to_next
            self.level += 1
            self.exp_to_next = int(self.exp_to_next * 1.2)
            return True  # 升级了
        return False
    
    def get_accuracy(self):
        """获取正确率"""
        if self.total_questions == 0:
            return 0
        return (self.correct_answers / self.total_questions) * 100

class GameModeSelector(QWidget):
    """游戏模式选择器"""
    mode_selected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("🎮 数学大冒险")
        title.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #333; margin: 30px; text-align: center;")
        layout.addWidget(title)
        
        subtitle = QLabel("选择你的挑战模式")
        subtitle.setFont(QFont("Microsoft YaHei", 14))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #666; margin-bottom: 30px;")
        layout.addWidget(subtitle)
        
        # 模式按钮
        modes_layout = QGridLayout()
        
        # 经典模式
        classic_btn = self.create_mode_button("📚 经典模式", "自由练习，稳步提升")
        classic_btn.clicked.connect(lambda: self.mode_selected.emit("classic"))
        modes_layout.addWidget(classic_btn, 0, 0)
        
        # 挑战模式
        challenge_btn = self.create_mode_button("⚡ 挑战模式", "60秒限时挑战")
        challenge_btn.clicked.connect(lambda: self.mode_selected.emit("challenge"))
        modes_layout.addWidget(challenge_btn, 0, 1)
        
        # 闯关模式
        adventure_btn = self.create_mode_button("🏰 闯关模式", "逐级解锁新挑战")
        adventure_btn.clicked.connect(lambda: self.mode_selected.emit("adventure"))
        modes_layout.addWidget(adventure_btn, 1, 0)
        
        # 竞速模式
        race_btn = self.create_mode_button("🏁 竞速模式", "速度与准确的较量")
        race_btn.clicked.connect(lambda: self.mode_selected.emit("race"))
        modes_layout.addWidget(race_btn, 1, 1)
        
        layout.addLayout(modes_layout)
        layout.addStretch()
    
    def create_mode_button(self, title, desc):
        """创建模式按钮"""
        btn = QPushButton(f"{title}\n{desc}")
        btn.setMinimumSize(200, 100)
        btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border: none;
                border-radius: 15px;
                color: white;
                font: bold 14px "Microsoft YaHei";
                padding: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7b8ceb, stop:1 #8a5aa3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6de9, stop:1 #6a4aa1);
            }
        """)
        return btn

class SimpleGameCalculator(QMainWindow):
    """简化版游戏化计算器"""
    
    def __init__(self):
        super().__init__()
        
        # 游戏数据
        self.stats = GameStats()
        self.current_problem = {}
        self.start_time = 0
        self.current_mode = "classic"
        self.time_limit = 0
        
        # 定时器
        self.game_timer = QTimer()
        self.game_timer.timeout.connect(self.update_timer)
        
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle('🎮 数学大冒险 - 简化版')
        self.setGeometry(100, 100, 800, 600)
        
        # 中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 状态栏
        self.create_status_bar(main_layout)
        
        # 堆叠widget
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)
        
        # 模式选择界面
        self.mode_selector = GameModeSelector()
        self.mode_selector.mode_selected.connect(self.start_game_mode)
        self.stacked_widget.addWidget(self.mode_selector)
        
        # 游戏界面
        self.game_widget = QWidget()
        self.setup_game_interface()
        self.stacked_widget.addWidget(self.game_widget)
        
        # 默认显示模式选择
        self.stacked_widget.setCurrentWidget(self.mode_selector)
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        # 等级和经验
        self.level_label = QLabel("🏆 等级: 1")
        self.level_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.level_label)
        
        self.exp_bar = QProgressBar()
        self.exp_bar.setMaximumWidth(150)
        self.exp_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid white;
                border-radius: 5px;
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #FFD700;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.exp_bar)
        
        # 分数和连击
        self.score_label = QLabel("💎 得分: 0")
        self.score_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.score_label)
        
        self.streak_label = QLabel("🔥 连击: 0")
        self.streak_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.streak_label)
        
        # 时间
        self.time_label = QLabel("⏱️ 时间: 00:00")
        self.time_label.setStyleSheet("color: white; font: bold 14px;")
        status_layout.addWidget(self.time_label)
        
        # 返回按钮
        back_btn = QPushButton("🏠 返回")
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255,255,255,0.2);
                border: 2px solid white;
                border-radius: 5px;
                color: white;
                font: bold 12px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: rgba(255,255,255,0.3);
            }
        """)
        back_btn.clicked.connect(self.return_to_menu)
        status_layout.addWidget(back_btn)
        
        layout.addWidget(status_frame)

    def setup_game_interface(self):
        """设置游戏界面"""
        layout = QVBoxLayout(self.game_widget)

        # 模式标题
        self.mode_title = QLabel("📚 经典模式")
        self.mode_title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        self.mode_title.setAlignment(Qt.AlignCenter)
        self.mode_title.setStyleSheet("color: #333; margin: 15px;")
        layout.addWidget(self.mode_title)

        # 题目显示
        problem_frame = QFrame()
        problem_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f093fb, stop:1 #f5576c);
                border-radius: 20px;
                margin: 10px;
                min-height: 150px;
            }
        """)
        problem_layout = QVBoxLayout(problem_frame)

        self.problem_label = QLabel("点击开始游戏")
        self.problem_label.setFont(QFont("Microsoft YaHei", 32, QFont.Bold))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setStyleSheet("color: white; padding: 30px;")
        problem_layout.addWidget(self.problem_label)

        self.difficulty_label = QLabel("🌟 难度: 简单")
        self.difficulty_label.setFont(QFont("Microsoft YaHei", 14))
        self.difficulty_label.setAlignment(Qt.AlignCenter)
        self.difficulty_label.setStyleSheet("color: white; margin-bottom: 10px;")
        problem_layout.addWidget(self.difficulty_label)

        layout.addWidget(problem_frame)

        # 设置区域
        settings_group = QGroupBox("⚙️ 游戏设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font: bold 14px;
                border: 2px solid #E0E0E0;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #FAFAFA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #333;
            }
        """)
        settings_layout = QGridLayout(settings_group)

        # 小数位数
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(1, 3)
        self.decimal_places.setValue(1)
        settings_layout.addWidget(self.decimal_places, 0, 1)

        # 难度滑块
        settings_layout.addWidget(QLabel("难度等级:"), 0, 2)
        self.difficulty_slider = QSlider(Qt.Horizontal)
        self.difficulty_slider.setRange(1, 5)
        self.difficulty_slider.setValue(1)
        self.difficulty_slider.valueChanged.connect(self.update_difficulty_display)
        settings_layout.addWidget(self.difficulty_slider, 0, 3)

        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        operators_layout = QHBoxLayout()

        self.add_cb = QCheckBox('➕ 加法')
        self.add_cb.setChecked(True)
        self.sub_cb = QCheckBox('➖ 减法')
        self.sub_cb.setChecked(True)
        self.mul_cb = QCheckBox('✖️ 乘法')
        self.div_cb = QCheckBox('➗ 除法')

        for cb in [self.add_cb, self.sub_cb, self.mul_cb, self.div_cb]:
            operators_layout.addWidget(cb)

        settings_layout.addLayout(operators_layout, 1, 1, 1, 3)
        layout.addWidget(settings_group)

        # 答案输入
        answer_frame = QFrame()
        answer_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        answer_layout = QHBoxLayout(answer_frame)

        answer_layout.addWidget(QLabel("💡 你的答案:"))

        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Microsoft YaHei", 18))
        self.answer_input.setStyleSheet("""
            QLineEdit {
                border: 3px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
                font-size: 18px;
                background-color: #F8F9FA;
            }
            QLineEdit:focus {
                border: 3px solid #4A90E2;
                background-color: white;
            }
        """)
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)

        layout.addWidget(answer_frame)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始游戏")
        self.start_btn.setMinimumHeight(50)
        self.start_btn.clicked.connect(self.start_game)
        button_layout.addWidget(self.start_btn)

        self.check_btn = QPushButton("✅ 检查答案")
        self.check_btn.setMinimumHeight(50)
        self.check_btn.clicked.connect(self.check_answer)
        self.check_btn.setEnabled(False)
        button_layout.addWidget(self.check_btn)

        self.next_btn = QPushButton("⏭️ 下一题")
        self.next_btn.setMinimumHeight(50)
        self.next_btn.clicked.connect(self.next_question)
        self.next_btn.setEnabled(False)
        button_layout.addWidget(self.next_btn)

        self.hint_btn = QPushButton("💡 提示")
        self.hint_btn.setMinimumHeight(50)
        self.hint_btn.clicked.connect(self.show_hint)
        self.hint_btn.setEnabled(False)
        button_layout.addWidget(self.hint_btn)

        # 应用按钮样式
        for btn in [self.start_btn, self.check_btn, self.next_btn, self.hint_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4A90E2, stop:1 #357ABD);
                    border: none;
                    border-radius: 10px;
                    color: white;
                    font: bold 14px;
                    padding: 15px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5BA0F2, stop:1 #4A90E2);
                }
                QPushButton:disabled {
                    background: #CCCCCC;
                    color: #666666;
                }
            """)

        layout.addLayout(button_layout)

        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumHeight(50)
        layout.addWidget(self.result_label)

    def start_game_mode(self, mode):
        """开始游戏模式"""
        self.current_mode = mode
        mode_titles = {
            "classic": "📚 经典模式",
            "challenge": "⚡ 挑战模式 (60秒)",
            "adventure": "🏰 闯关模式",
            "race": "🏁 竞速模式 (30秒)"
        }

        self.mode_title.setText(mode_titles.get(mode, "📚 经典模式"))

        # 设置时间限制
        if mode == "challenge":
            self.time_limit = 60
        elif mode == "race":
            self.time_limit = 30
        else:
            self.time_limit = 0

        self.stacked_widget.setCurrentWidget(self.game_widget)

    def return_to_menu(self):
        """返回主菜单"""
        self.game_timer.stop()
        self.stacked_widget.setCurrentWidget(self.mode_selector)
        self.reset_game()

    def reset_game(self):
        """重置游戏"""
        self.stats.reset()
        self.start_btn.setEnabled(True)
        self.check_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.hint_btn.setEnabled(False)
        self.problem_label.setText("点击开始游戏")
        self.result_label.clear()
        self.answer_input.clear()
        self.update_status_display()

    def start_game(self):
        """开始游戏"""
        self.start_btn.setEnabled(False)
        self.check_btn.setEnabled(True)
        self.hint_btn.setEnabled(True)

        if self.time_limit > 0:
            self.game_timer.start(1000)

        self.generate_problem()

    def generate_problem(self):
        """生成题目"""
        decimal_places = self.decimal_places.value()
        difficulty = self.difficulty_slider.value()

        # 根据难度设置数值范围
        ranges = {1: (0.1, 2.9), 2: (0.1, 4.9), 3: (0.1, 9.9),
                 4: (0.1, 19.9), 5: (0.1, 99.9)}
        min_val, max_val = ranges[difficulty]

        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)

        # 获取运算符
        operators = []
        if self.add_cb.isChecked(): operators.append('+')
        if self.sub_cb.isChecked(): operators.append('-')
        if self.mul_cb.isChecked(): operators.append('×')
        if self.div_cb.isChecked(): operators.append('÷')

        if not operators:
            operators = ['+']

        op = random.choice(operators)

        # 确保结果合理
        if op == '-' and a < b:
            a, b = b, a
        elif op == '÷':
            if b > a:
                a, b = b, a
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)

        # 计算答案
        if op == '+':
            answer = a + b
        elif op == '-':
            answer = a - b
        elif op == '×':
            answer = a * b
        else:  # ÷
            answer = a / b

        self.current_problem = {
            'a': a, 'b': b, 'op': op, 'answer': round(answer, decimal_places)
        }

        # 显示题目
        self.problem_label.setText(f"{a} {op} {b} = ?")
        self.start_time = time.time()
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()
        self.next_btn.setEnabled(True)

    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            correct_answer = self.current_problem['answer']
            answer_time = time.time() - self.start_time
            difficulty = self.difficulty_slider.value()

            if abs(user_answer - correct_answer) < 0.001:
                # 答对了
                score_gained, exp_gained = self.stats.add_correct_answer(answer_time, difficulty)

                # 显示结果
                time_text = f"{answer_time:.1f}秒"
                if answer_time < 3:
                    time_text += " ⚡闪电般！"
                elif answer_time < 5:
                    time_text += " 🚀很快！"

                self.result_label.setText(
                    f"🎉 正确！答案是 {correct_answer}\n"
                    f"⏱️ 用时: {time_text}\n"
                    f"💎 得分: +{score_gained} ✨ 经验: +{exp_gained}"
                )
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        background: #E8F5E8;
                        border: 2px solid #4CAF50;
                        border-radius: 10px;
                        padding: 10px;
                    }
                """)

                # 检查升级
                if self.stats.level > (self.stats.level - 1):
                    QMessageBox.information(
                        self, "🎉 升级！",
                        f"恭喜升级到 {self.stats.level} 级！"
                    )

            else:
                # 答错了
                self.stats.add_wrong_answer()
                self.result_label.setText(f"❌ 错误！正确答案是 {correct_answer}\n💪 继续加油！")
                self.result_label.setStyleSheet("""
                    QLabel {
                        color: #F44336;
                        background: #FFEBEE;
                        border: 2px solid #F44336;
                        border-radius: 10px;
                        padding: 10px;
                    }
                """)

            self.update_status_display()

        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

    def next_question(self):
        """下一题"""
        self.generate_problem()

    def show_hint(self):
        """显示提示"""
        problem = self.current_problem
        difficulty = self.difficulty_slider.value()

        if difficulty <= 2:
            # 简单提示
            if problem['op'] == '+':
                hint = f"💡 提示：{problem['a']} + {problem['b']}\n把两个数相加！"
            elif problem['op'] == '-':
                hint = f"💡 提示：{problem['a']} - {problem['b']}\n用大数减小数！"
            elif problem['op'] == '×':
                hint = f"💡 提示：{problem['a']} × {problem['b']}\n两个数相乘！"
            else:
                hint = f"💡 提示：{problem['a']} ÷ {problem['b']}\n第一个数除以第二个数！"
        else:
            # 困难提示
            answer_str = str(problem['answer'])
            if len(answer_str) > 1:
                hint_answer = answer_str[0] + "?" * (len(answer_str) - 1)
                hint = f"💡 提示：答案开头是 {answer_str[0]}\n完整答案：{hint_answer}"
            else:
                hint = "💡 提示：答案是一位数！"

        QMessageBox.information(self, "💡 智能提示", hint)

    def update_timer(self):
        """更新计时器"""
        if self.time_limit > 0:
            self.time_limit -= 1
            minutes = self.time_limit // 60
            seconds = self.time_limit % 60
            self.time_label.setText(f"⏱️ 剩余: {minutes:02d}:{seconds:02d}")

            if self.time_limit <= 0:
                self.end_timed_mode()
        else:
            # 普通计时
            elapsed = int(time.time() - self.start_time) if hasattr(self, 'start_time') else 0
            minutes = elapsed // 60
            seconds = elapsed % 60
            self.time_label.setText(f"⏱️ 时间: {minutes:02d}:{seconds:02d}")

    def end_timed_mode(self):
        """结束限时模式"""
        self.game_timer.stop()

        mode_name = "挑战" if self.current_mode == "challenge" else "竞速"

        QMessageBox.information(
            self,
            f"⏰ {mode_name}结束！",
            f"🎯 {mode_name}模式结束！\n\n"
            f"📊 最终统计：\n"
            f"✅ 答对: {self.stats.correct_answers} 题\n"
            f"❌ 答错: {self.stats.total_questions - self.stats.correct_answers} 题\n"
            f"💎 总得分: {self.stats.score}\n"
            f"🔥 最高连击: {self.stats.max_streak}\n"
            f"📈 正确率: {self.stats.get_accuracy():.1f}%\n"
            f"⚡ 最快用时: {self.stats.best_time:.1f}秒\n\n"
            f"🏆 {'表现优秀！' if self.stats.get_accuracy() >= 80 else '继续加油！'}"
        )
        self.return_to_menu()

    def update_status_display(self):
        """更新状态显示"""
        self.level_label.setText(f"🏆 等级: {self.stats.level}")

        progress = (self.stats.exp / self.stats.exp_to_next) * 100
        self.exp_bar.setValue(int(progress))
        self.exp_bar.setFormat(f"{self.stats.exp}/{self.stats.exp_to_next} EXP")

        self.score_label.setText(f"💎 得分: {self.stats.score}")
        self.streak_label.setText(f"🔥 连击: {self.stats.streak}")

    def update_difficulty_display(self):
        """更新难度显示"""
        difficulty_names = {
            1: "🌟 简单", 2: "🌟🌟 较简单", 3: "🌟🌟🌟 中等",
            4: "🌟🌟🌟🌟 较难", 5: "🌟🌟🌟🌟🌟 困难"
        }
        difficulty = self.difficulty_slider.value()
        self.difficulty_label.setText(f"难度: {difficulty_names.get(difficulty, '🌟 简单')}")

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
            QWidget {
                font-family: "Microsoft YaHei";
            }
            QSpinBox {
                border: 2px solid #E0E0E0;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
                min-width: 60px;
            }
            QSpinBox:focus {
                border: 2px solid #4A90E2;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: #B1B1B1;
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #4A90E2;
                border: 1px solid #357ABD;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QCheckBox {
                font: 12px;
                color: #333;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid #CCCCCC;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4A90E2;
                border: 2px solid #4A90E2;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("数学大冒险")
    app.setApplicationVersion("1.0")

    game = SimpleGameCalculator()
    game.show()

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
