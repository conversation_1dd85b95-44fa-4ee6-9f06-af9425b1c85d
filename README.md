# 小数运算练习器

这是一个使用PyQt/PySide创建的小数运算练习程序，可以自定义小数位数和运算符。

## 功能特点

- **自定义小数位数**：可以选择1-3位小数
- **多种运算符**：支持加法(+)、减法(-)、乘法(×)、除法(÷)
- **智能题目生成**：
  - 减法确保结果为正数
  - 除法确保结果为合理的小数
- **实时答案检查**：输入答案后立即显示正确与否
- **友好的用户界面**：清晰的题目显示和操作按钮

## 文件说明

- `decimal_calculator_gui.py` - 使用PyQt5的版本
- `decimal_calculator_pyside.py` - 使用PySide2的版本
- `mo.py` - 原始的批量生成Excel题目的脚本

## 安装依赖

### 使用PyQt5版本：
```bash
pip install PyQt5
```

### 使用PySide2版本：
```bash
pip install PySide2
```

## 运行程序

### 运行PyQt5版本：
```bash
python decimal_calculator_gui.py
```

### 运行PySide2版本：
```bash
python decimal_calculator_pyside.py
```

## 使用说明

1. **设置小数位数**：使用数字选择框选择1-3位小数
2. **选择运算符**：勾选你想要练习的运算符（+、-、×、÷）
3. **答题**：在答案输入框中输入答案，按回车或点击"检查答案"
4. **下一题**：点击"下一题"按钮生成新的题目

## 界面预览

程序界面包含：
- 设置区域：小数位数选择和运算符选择
- 题目显示区域：大字体显示当前题目
- 答案输入区域：输入答案的文本框
- 按钮区域：检查答案和下一题按钮
- 结果显示区域：显示答案正确与否

## 特殊处理

- **减法运算**：自动确保被减数大于减数，避免负数结果
- **除法运算**：调整数值确保结果为合理的小数
- **输入验证**：检查用户输入是否为有效数字
- **浮点精度**：允许小的浮点误差，提高用户体验
