import sys
import random
import pandas as pd
from PyQt5.QtWidgets import <PERSON>Application, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QMessageBox
from PyQt5.QtCore import Qt

class SimpleDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
    
    def initUI(self):
        self.setWindowTitle('Excel生成演示')
        self.setGeometry(300, 300, 400, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        self.label = QLabel('点击按钮生成Excel文件')
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)
        
        self.button = QPushButton('生成Excel文件')
        self.button.clicked.connect(self.generate_excel)
        layout.addWidget(self.button)
    
    def generate_excel(self):
        try:
            # 生成简单的测试数据
            data = []
            for i in range(100):  # 生成100道题
                a = round(random.uniform(0.1, 9.9), 1)
                b = round(random.uniform(0.1, 9.9), 1)
                op = random.choice(['+', '-'])
                if op == '-' and a < b:
                    a, b = b, a
                data.append([a, op, b, '='])
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=['第一个数', '运算符', '第二个数', '等号'])
            
            # 保存到Excel
            filename = 'demo_test.xlsx'
            df.to_excel(filename, index=False)
            
            QMessageBox.information(self, '成功', f'已生成{len(data)}道题目到文件：{filename}')
            
        except Exception as e:
            QMessageBox.critical(self, '错误', f'生成失败：{str(e)}')

def main():
    app = QApplication(sys.argv)
    window = SimpleDemo()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
