import random
import pandas as pd

def generate_decimal_numbers(decimal_places=1):
    """根据设置生成两个小数"""
    min_val = 0.1
    max_val = 9.9
    
    # 根据小数位数调整范围
    if decimal_places == 2:
        max_val = 9.99
    elif decimal_places == 3:
        max_val = 9.999
    
    a = round(random.uniform(min_val, max_val), decimal_places)
    b = round(random.uniform(min_val, max_val), decimal_places)
    return a, b

def ensure_positive_result(a, b, op, decimal_places=1):
    """确保减法结果不为负数，除法结果为整数或简单小数"""
    if op == '-' and a < b:
        return b, a
    elif op == '÷':
        # 确保除法结果是合理的小数
        if b > a:
            a, b = b, a
        # 调整b使得结果是简单的小数
        result = round(a / b, decimal_places)
        b = round(a / result, decimal_places)
    return a, b

def generate_problem_data(operators=['+', '-'], decimal_places=1):
    """生成单个题目的数据，返回独立的单元格数据"""
    a, b = generate_decimal_numbers(decimal_places)
    op = random.choice(operators)
    a, b = ensure_positive_result(a, b, op, decimal_places)
    return a, op, b, '='

def generate_excel_file(operators=['+', '-'], decimal_places=1, filename=None):
    """生成1000题Excel文件"""
    try:
        # 生成1000道题目
        problems_data = []
        for i in range(1000):
            problem = generate_problem_data(operators, decimal_places)
            problems_data.append(problem)
        
        # 创建一个包含所有列的字典，每列都有20行
        final_data = {}
        num_rows = 20  # 每列20行
        
        # 为50组题目创建列
        for group in range(50):
            start_idx = group * 20
            group_problems = problems_data[start_idx:start_idx + 20]
            
            # 为每组创建4列：第一个数、运算符、第二个数、等号
            col_base = f"第{group+1}组"
            final_data[f'{col_base}_第一个数'] = [p[0] for p in group_problems]
            final_data[f'{col_base}_运算符'] = [p[1] for p in group_problems]
            final_data[f'{col_base}_第二个数'] = [p[2] for p in group_problems]
            final_data[f'{col_base}_等号'] = [p[3] for p in group_problems]
            
            # 在每组后添加空白列（除了最后一组）
            if group < 49:
                final_data[f'空白_{group+1}'] = ['' for _ in range(20)]
        
        # 创建DataFrame
        df = pd.DataFrame(final_data)
        
        # 生成文件名
        if not filename:
            operators_str = ''.join(operators)
            filename = f'小数运算练习_{decimal_places}位小数_{operators_str}_1000题.xlsx'
        
        # 保存到Excel文件
        df.to_excel(filename, index=False)
        
        print(f"已成功生成1000道题目！")
        print(f"文件保存为：{filename}")
        print(f"小数位数：{decimal_places}位")
        print(f"运算符：{', '.join(operators)}")
        print(f"题目按每20题一组分为50列组合")
        print(f"DataFrame形状: {df.shape}")
        print("\n前5行预览：")
        print(df.head())
        
        return True
        
    except Exception as e:
        print(f"生成Excel文件时出错：{str(e)}")
        return False

if __name__ == '__main__':
    # 测试不同配置
    print("=== 测试1：1位小数，加减法 ===")
    generate_excel_file(['+', '-'], 1, '测试_1位小数_加减法.xlsx')
    
    print("\n=== 测试2：2位小数，四则运算 ===")
    generate_excel_file(['+', '-', '×', '÷'], 2, '测试_2位小数_四则运算.xlsx')
    
    print("\n=== 测试3：1位小数，仅加法 ===")
    generate_excel_file(['+'], 1, '测试_1位小数_仅加法.xlsx')
