
import random
import pandas as pd

def generate_decimal_numbers():
    """生成两个小数，精确到1位小数"""
    a = round(random.uniform(0.1, 9.9), 1)
    b = round(random.uniform(0.1, 9.9), 1)
    return a, b

def generate_operator():
    """生成运算符"""
    return random.choice(['×', '÷'])

def ensure_integer_division(a, b, op):
    """确保除法结果为整数"""
    if op == '÷':
        # 将被除数和除数都乘以10转换为整数
        a_int = int(a * 10)
        b_int = int(b * 10)
        # 确保除数不为0且能整除
        while b_int == 0 or a_int % b_int != 0:
            a, b = generate_decimal_numbers()
            a_int = int(a * 10)
            b_int = int(b * 10)
        return a, b
    return a, b

def generate_problem_data():
    """生成单个题目的数据，返回独立的单元格数据"""
    a, b = generate_decimal_numbers()
    op = generate_operator()
    a, b = ensure_integer_division(a, b, op)
    return a, op, b, '='

# 生成1000道题目
problems_data = []
for i in range(1000):
    problem = generate_problem_data()
    problems_data.append(problem)

# 创建一个包含所有列的字典，每列都有20行
final_data = {}
num_rows = 20  # 每列20行

# 为50组题目创建列
for group in range(50):
    start_idx = group * 20
    group_problems = problems_data[start_idx:start_idx + 20]

    # 为每组创建4列：第一个数、运算符、第二个数、等号
    col_base = f"第{group+1}组"
    final_data[f'{col_base}_第一个数'] = [p[0] for p in group_problems]
    final_data[f'{col_base}_运算符'] = [p[1] for p in group_problems]
    final_data[f'{col_base}_第二个数'] = [p[2] for p in group_problems]
    final_data[f'{col_base}_等号'] = [p[3] for p in group_problems]

    # 在每组后添加空白列（除了最后一组）
    if group < 49:
        final_data[f'空白_{group+1}'] = ['' for _ in range(20)]

# 创建DataFrame
df = pd.DataFrame(final_data)

# 保存到Excel文件
df.to_excel('小学四年级小数乘除1000题_分列版.xlsx', index=False)

print("已生成1000道小数乘除法题目！")
print("文件保存为：小学四年级小数乘除1000题_分列版.xlsx")
print("题目按每20题一组分为50列组合")
print(f"DataFrame形状: {df.shape}")
print("\n前5行预览：")
print(df.head())
