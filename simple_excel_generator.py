import random
import openpyxl
from openpyxl import Workbook

def generate_decimal_numbers():
    """生成两个小数，精确到1位小数"""
    a = round(random.uniform(0.1, 9.9), 1)
    b = round(random.uniform(0.1, 9.9), 1)
    return a, b

def generate_operator():
    """生成运算符"""
    return random.choice(['+', '-'])

def ensure_positive_result(a, b, op):
    """确保减法结果不为负数"""
    if op == '-' and a < b:
        return b, a
    return a, b

def calculate_result(a, b, op):
    """计算结果"""
    if op == '+':
        return round(a + b, 1)
    elif op == '-':
        return round(a - b, 1)
    else:
        return 0

def generate_problem_data():
    """生成单个题目的数据，返回独立的单元格数据，包含结果"""
    a, b = generate_decimal_numbers()
    op = generate_operator()
    a, b = ensure_positive_result(a, b, op)
    result = calculate_result(a, b, op)
    return a, op, b, '=', result

def create_excel_with_openpyxl():
    """使用openpyxl创建Excel文件"""
    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "小数运算练习题"
    
    # 生成1000道题目
    problems_data = []
    for i in range(1000):
        problem = generate_problem_data()
        problems_data.append(problem)
    
    print("题目生成完成，开始写入Excel...")
    
    # 写入表头和数据
    col_index = 1
    
    for group in range(50):  # 50组，每组20题
        start_idx = group * 20
        group_problems = problems_data[start_idx:start_idx + 20]
        
        # 写入表头
        group_name = f"第{group+1}组"
        ws.cell(row=1, column=col_index, value=f"{group_name}_数字A")
        ws.cell(row=1, column=col_index+1, value=f"{group_name}_运算符")
        ws.cell(row=1, column=col_index+2, value=f"{group_name}_数字B")
        ws.cell(row=1, column=col_index+3, value=f"{group_name}_等号")
        ws.cell(row=1, column=col_index+4, value=f"{group_name}_结果")
        
        # 写入数据
        for row_idx, problem in enumerate(group_problems, start=2):
            ws.cell(row=row_idx, column=col_index, value=problem[0])      # 数字A
            ws.cell(row=row_idx, column=col_index+1, value=problem[1])    # 运算符
            ws.cell(row=row_idx, column=col_index+2, value=problem[2])    # 数字B
            ws.cell(row=row_idx, column=col_index+3, value=problem[3])    # 等号
            ws.cell(row=row_idx, column=col_index+4, value=problem[4])    # 结果
        
        # 移动到下一组的列位置（5列数据 + 1列空白）
        col_index += 6
        
        # 进度显示
        if (group + 1) % 10 == 0:
            print(f"已完成 {group + 1}/50 组...")
    
    # 保存文件
    filename = '小数运算练习题_完整版.xlsx'
    wb.save(filename)
    
    print(f"\n✅ 成功生成Excel文件：{filename}")
    print(f"📊 包含1000道题目，分为50组，每组20题")
    print(f"📋 每个题目格式：数字A | 运算符 | 数字B | 等号 | 结果")
    print(f"📈 Excel表格大小：21行 × {col_index-1}列")
    
    return filename

def create_simple_text_version():
    """创建简单的文本版本用于预览"""
    print("生成前10题预览：")
    print("-" * 50)
    
    for i in range(10):
        a, op, b, eq, result = generate_problem_data()
        print(f"{i+1:2d}. {a} {op} {b} {eq} {result}")
    
    print("-" * 50)

if __name__ == "__main__":
    print("🧮 小数运算练习题生成器")
    print("=" * 50)
    
    # 先显示预览
    create_simple_text_version()
    
    try:
        # 尝试生成Excel文件
        filename = create_excel_with_openpyxl()
        print(f"\n🎉 文件已保存到：{filename}")
        
    except ImportError:
        print("\n❌ 缺少openpyxl库，请安装：pip install openpyxl")
    except Exception as e:
        print(f"\n❌ 生成Excel文件时出错：{e}")
        print("但您可以参考上面的预览格式手动创建。")
