
import random
import pandas as pd

def generate_decimal_numbers():
    """生成两个小数，精确到1位小数"""
    a = round(random.uniform(0.1, 9.9), 1)
    b = round(random.uniform(0.1, 9.9), 1)
    return a, b

def generate_operator():
    """随机生成加减乘除运算符"""
    return random.choice(['+', '-', '×', '÷'])

def adjust_numbers(a, b, op):
    """根据运算符调整数字"""
    if op == '-':
        return (b, a) if a < b else (a, b)
    elif op == '÷':
        # 确保除法结果为整数
        a_int = int(a * 10)
        b_int = int(b * 10)
        while b_int == 0 or a_int % b_int != 0:
            a, b = generate_decimal_numbers()
            a_int = int(a * 10)
            b_int = int(b * 10)
    return a, b

def generate_problem():
    """生成单个题目"""
    a, b = generate_decimal_numbers()
    op = generate_operator()
    a, b = adjust_numbers(a, b, op)
    return f"{a} {op} {b} ="

# 生成500道题目
problems = [generate_problem() for _ in range(500)]

# 创建DataFrame，每20题一组
df = pd.DataFrame({
    f"第{i+1}组": problems[i*20:(i+1)*20] for i in range(25)
})

# 保存到Excel
df.to_excel('小数加减乘除500题.xlsx', index=False)
