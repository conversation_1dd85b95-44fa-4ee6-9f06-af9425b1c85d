#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动增强版小数运算练习器
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_calculator_gui import main
    print("🚀 启动增强版小数运算练习器...")
    main()
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所需依赖：")
    print("pip install PyQt5 pandas openpyxl")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    input("按回车键退出...")
