import sys
import random
import pandas as pd
import time
import json
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton,
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox,
                             QFileDialog, QProgressBar, QTabWidget, QTextEdit,
                             QSlider, QFrame, QSplashScreen, QComboBox, QLCDNumber,
                             QGraphicsDropShadowEffect, QStackedWidget)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QRect, pyqtSignal, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor, QLinearGradient, QPainter, QIcon
from PyQt5.QtMultimedia import QSound

class GameSplashScreen(QSplashScreen):
    """游戏启动画面"""
    def __init__(self):
        super().__init__()
        self.setFixedSize(400, 300)
        self.setStyleSheet("""
            QSplashScreen {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
            }
        """)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制标题
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        painter.drawText(self.rect(), Qt.AlignCenter, "🎮 数学大冒险\n\n小数运算挑战赛")

class AchievementSystem:
    """成就系统"""
    def __init__(self):
        self.achievements = {
            'first_correct': {'name': '🎯 初出茅庐', 'desc': '答对第一题', 'unlocked': False},
            'streak_5': {'name': '🔥 连击新手', 'desc': '连续答对5题', 'unlocked': False},
            'streak_10': {'name': '⚡ 连击高手', 'desc': '连续答对10题', 'unlocked': False},
            'streak_20': {'name': '💫 连击大师', 'desc': '连续答对20题', 'unlocked': False},
            'speed_demon': {'name': '⚡ 闪电侠', 'desc': '3秒内答对一题', 'unlocked': False},
            'century': {'name': '💯 百题达人', 'desc': '累计答对100题', 'unlocked': False},
            'perfectionist': {'name': '🏆 完美主义者', 'desc': '正确率达到95%', 'unlocked': False},
            'math_wizard': {'name': '🧙‍♂️ 数学法师', 'desc': '掌握所有运算符', 'unlocked': False}
        }
        self.load_achievements()
    
    def check_achievement(self, key, condition):
        """检查成就"""
        if key in self.achievements and not self.achievements[key]['unlocked'] and condition:
            self.achievements[key]['unlocked'] = True
            self.save_achievements()
            return self.achievements[key]
        return None
    
    def save_achievements(self):
        """保存成就"""
        try:
            with open('achievements.json', 'w', encoding='utf-8') as f:
                json.dump(self.achievements, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def load_achievements(self):
        """加载成就"""
        try:
            with open('achievements.json', 'r', encoding='utf-8') as f:
                saved = json.load(f)
                for key in saved:
                    if key in self.achievements:
                        self.achievements[key]['unlocked'] = saved[key]['unlocked']
        except:
            pass

class LevelSystem:
    """等级系统"""
    def __init__(self):
        self.level = 1
        self.exp = 0
        self.exp_to_next = 100
        
    def add_exp(self, points):
        """增加经验值"""
        self.exp += points
        while self.exp >= self.exp_to_next:
            self.exp -= self.exp_to_next
            self.level += 1
            self.exp_to_next = int(self.exp_to_next * 1.2)  # 每级所需经验递增
        return self.level
    
    def get_level_info(self):
        """获取等级信息"""
        return {
            'level': self.level,
            'exp': self.exp,
            'exp_to_next': self.exp_to_next,
            'progress': self.exp / self.exp_to_next * 100
        }

class GameModeWidget(QWidget):
    """游戏模式选择"""
    mode_selected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("🎮 选择游戏模式")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #333; margin: 20px;")
        layout.addWidget(title)
        
        # 游戏模式按钮
        modes_layout = QGridLayout()
        
        # 经典模式
        classic_btn = self.create_mode_button(
            "📚 经典模式", 
            "传统练习模式\n自由选择题型和难度",
            "#4A90E2"
        )
        classic_btn.clicked.connect(lambda: self.mode_selected.emit("classic"))
        modes_layout.addWidget(classic_btn, 0, 0)
        
        # 挑战模式
        challenge_btn = self.create_mode_button(
            "⚡ 挑战模式",
            "限时答题挑战\n测试你的反应速度",
            "#FF6B6B"
        )
        challenge_btn.clicked.connect(lambda: self.mode_selected.emit("challenge"))
        modes_layout.addWidget(challenge_btn, 0, 1)
        
        # 闯关模式
        adventure_btn = self.create_mode_button(
            "🏰 闯关模式",
            "逐级挑战关卡\n解锁新的题型",
            "#4ECDC4"
        )
        adventure_btn.clicked.connect(lambda: self.mode_selected.emit("adventure"))
        modes_layout.addWidget(adventure_btn, 1, 0)
        
        # 竞速模式
        race_btn = self.create_mode_button(
            "🏁 竞速模式",
            "看谁答得又快又准\n挑战最佳记录",
            "#45B7D1"
        )
        race_btn.clicked.connect(lambda: self.mode_selected.emit("race"))
        modes_layout.addWidget(race_btn, 1, 1)
        
        layout.addLayout(modes_layout)
        layout.addStretch()
    
    def create_mode_button(self, title, desc, color):
        """创建模式按钮"""
        btn = QPushButton()
        btn.setMinimumSize(200, 120)
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border: none;
                border-radius: 15px;
                color: white;
                font: bold 14px "Microsoft YaHei";
                text-align: left;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(color)}, stop:1 {color});
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.darken_color(color)}, stop:1 {self.darken_color(color, 0.3)});
            }}
        """)
        
        # 设置按钮文本
        btn.setText(f"{title}\n\n{desc}")
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 3)
        btn.setGraphicsEffect(shadow)
        
        return btn
    
    def lighten_color(self, color, factor=0.2):
        """颜色变亮"""
        # 简单的颜色处理
        return color.replace('#', '#F')[:7]  # 简化处理
    
    def darken_color(self, color, factor=0.2):
        """颜色变暗"""
        # 简单的颜色处理
        return color.replace('#', '#2')[:7]  # 简化处理

class GameCalculator(QMainWindow):
    """游戏化数学计算器"""
    
    def __init__(self):
        super().__init__()
        
        # 游戏数据
        self.current_a = 0
        self.current_b = 0
        self.current_op = '+'
        self.correct_answer = 0
        self.start_time = 0
        
        # 统计数据
        self.score = 0
        self.total_questions = 0
        self.streak = 0
        self.max_streak = 0
        self.total_time = 0
        self.best_time = float('inf')
        
        # 游戏系统
        self.achievement_system = AchievementSystem()
        self.level_system = LevelSystem()
        
        # 游戏模式
        self.current_mode = "classic"
        self.time_limit = 0
        self.challenge_questions = 0
        
        # 定时器
        self.game_timer = QTimer()
        self.game_timer.timeout.connect(self.update_timer)
        
        self.init_ui()
        self.apply_game_style()
        
        # 显示启动画面
        self.show_splash_screen()
    
    def show_splash_screen(self):
        """显示启动画面"""
        self.splash = GameSplashScreen()
        self.splash.show()
        
        # 3秒后关闭启动画面
        QTimer.singleShot(3000, self.splash.close)
        QTimer.singleShot(3000, self.show)
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle('🎮 数学大冒险 - 小数运算挑战赛')
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(900, 600)
        
        # 中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部状态栏
        self.create_status_bar(main_layout)
        
        # 堆叠widget用于切换不同界面
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)
        
        # 游戏模式选择界面
        self.mode_widget = GameModeWidget()
        self.mode_widget.mode_selected.connect(self.start_game_mode)
        self.stacked_widget.addWidget(self.mode_widget)
        
        # 游戏界面
        self.game_widget = QWidget()
        self.setup_game_interface()
        self.stacked_widget.addWidget(self.game_widget)
        
        # 默认显示模式选择
        self.stacked_widget.setCurrentWidget(self.mode_widget)
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        # 等级显示
        self.level_label = QLabel("🏆 等级: 1")
        self.level_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        status_layout.addWidget(self.level_label)
        
        # 经验条
        self.exp_bar = QProgressBar()
        self.exp_bar.setMaximumWidth(150)
        self.exp_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid white;
                border-radius: 5px;
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #FFD700;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.exp_bar)
        
        # 分数显示
        self.score_label = QLabel("💎 得分: 0")
        self.score_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        status_layout.addWidget(self.score_label)
        
        # 连击显示
        self.streak_label = QLabel("🔥 连击: 0")
        self.streak_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        status_layout.addWidget(self.streak_label)
        
        # 时间显示
        self.time_label = QLabel("⏱️ 时间: 00:00")
        self.time_label.setStyleSheet("color: white; font: bold 14px 'Microsoft YaHei';")
        status_layout.addWidget(self.time_label)
        
        # 返回按钮
        back_btn = QPushButton("🏠 返回")
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255,255,255,0.2);
                border: 2px solid white;
                border-radius: 5px;
                color: white;
                font: bold 12px 'Microsoft YaHei';
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: rgba(255,255,255,0.3);
            }
        """)
        back_btn.clicked.connect(self.return_to_menu)
        status_layout.addWidget(back_btn)
        
        layout.addWidget(status_frame)

    def setup_game_interface(self):
        """设置游戏界面"""
        layout = QVBoxLayout(self.game_widget)

        # 游戏模式标题
        self.mode_title = QLabel("📚 经典模式")
        self.mode_title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        self.mode_title.setAlignment(Qt.AlignCenter)
        self.mode_title.setStyleSheet("color: #333; margin: 10px;")
        layout.addWidget(self.mode_title)

        # 题目显示区域
        problem_frame = QFrame()
        problem_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f093fb, stop:1 #f5576c);
                border-radius: 20px;
                margin: 10px;
            }
        """)
        problem_layout = QVBoxLayout(problem_frame)

        # 题目文本
        self.problem_label = QLabel("准备开始...")
        self.problem_label.setFont(QFont("Microsoft YaHei", 36, QFont.Bold))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setStyleSheet("color: white; padding: 30px;")
        problem_layout.addWidget(self.problem_label)

        # 难度指示器
        self.difficulty_label = QLabel("🌟 难度: 简单")
        self.difficulty_label.setFont(QFont("Microsoft YaHei", 14))
        self.difficulty_label.setAlignment(Qt.AlignCenter)
        self.difficulty_label.setStyleSheet("color: white; margin-bottom: 10px;")
        problem_layout.addWidget(self.difficulty_label)

        layout.addWidget(problem_frame)

        # 设置区域
        settings_group = QGroupBox("⚙️ 游戏设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font: bold 14px "Microsoft YaHei";
                border: 2px solid #E0E0E0;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #FAFAFA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #333;
            }
        """)
        settings_layout = QGridLayout(settings_group)

        # 小数位数
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places_spinbox = QSpinBox()
        self.decimal_places_spinbox.setRange(1, 3)
        self.decimal_places_spinbox.setValue(1)
        self.decimal_places_spinbox.setStyleSheet(self.get_spinbox_style())
        settings_layout.addWidget(self.decimal_places_spinbox, 0, 1)

        # 难度滑块
        settings_layout.addWidget(QLabel("难度等级:"), 0, 2)
        self.difficulty_slider = QSlider(Qt.Horizontal)
        self.difficulty_slider.setRange(1, 5)
        self.difficulty_slider.setValue(1)
        self.difficulty_slider.setStyleSheet(self.get_slider_style())
        self.difficulty_slider.valueChanged.connect(self.update_difficulty_display)
        settings_layout.addWidget(self.difficulty_slider, 0, 3)

        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        operators_layout = QHBoxLayout()

        self.add_checkbox = QCheckBox('➕ 加法')
        self.add_checkbox.setChecked(True)
        self.subtract_checkbox = QCheckBox('➖ 减法')
        self.subtract_checkbox.setChecked(True)
        self.multiply_checkbox = QCheckBox('✖️ 乘法')
        self.divide_checkbox = QCheckBox('➗ 除法')

        for checkbox in [self.add_checkbox, self.subtract_checkbox,
                        self.multiply_checkbox, self.divide_checkbox]:
            checkbox.setStyleSheet(self.get_checkbox_style())
            operators_layout.addWidget(checkbox)

        settings_layout.addLayout(operators_layout, 1, 1, 1, 3)
        layout.addWidget(settings_group)

        # 答案输入区域
        answer_frame = QFrame()
        answer_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #E0E0E0;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        answer_layout = QVBoxLayout(answer_frame)

        # 答案输入
        input_layout = QHBoxLayout()
        answer_icon = QLabel("💡")
        answer_icon.setFont(QFont("Segoe UI Emoji", 24))
        input_layout.addWidget(answer_icon)

        answer_label = QLabel("你的答案:")
        answer_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        input_layout.addWidget(answer_label)

        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Microsoft YaHei", 20))
        self.answer_input.setStyleSheet("""
            QLineEdit {
                border: 3px solid #E0E0E0;
                border-radius: 10px;
                padding: 15px;
                font-size: 20px;
                background-color: #F8F9FA;
            }
            QLineEdit:focus {
                border: 3px solid #4A90E2;
                background-color: white;
            }
        """)
        self.answer_input.returnPressed.connect(self.check_answer)
        input_layout.addWidget(self.answer_input)

        answer_layout.addLayout(input_layout)
        layout.addWidget(answer_frame)

        # 游戏控制按钮
        button_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 开始游戏")
        self.start_btn.setMinimumHeight(50)
        self.start_btn.setStyleSheet(self.get_button_style("#4CAF50"))
        self.start_btn.clicked.connect(self.start_game)
        button_layout.addWidget(self.start_btn)

        self.check_btn = QPushButton("✅ 检查答案")
        self.check_btn.setMinimumHeight(50)
        self.check_btn.setStyleSheet(self.get_button_style("#2196F3"))
        self.check_btn.clicked.connect(self.check_answer)
        self.check_btn.setEnabled(False)
        button_layout.addWidget(self.check_btn)

        self.next_btn = QPushButton("⏭️ 下一题")
        self.next_btn.setMinimumHeight(50)
        self.next_btn.setStyleSheet(self.get_button_style("#FF9800"))
        self.next_btn.clicked.connect(self.next_question)
        self.next_btn.setEnabled(False)
        button_layout.addWidget(self.next_btn)

        self.hint_btn = QPushButton("💡 提示")
        self.hint_btn.setMinimumHeight(50)
        self.hint_btn.setStyleSheet(self.get_button_style("#9C27B0"))
        self.hint_btn.clicked.connect(self.show_hint)
        self.hint_btn.setEnabled(False)
        button_layout.addWidget(self.hint_btn)

        layout.addLayout(button_layout)

        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumHeight(60)
        layout.addWidget(self.result_label)

    def start_game_mode(self, mode):
        """开始游戏模式"""
        self.current_mode = mode
        mode_titles = {
            "classic": "📚 经典模式",
            "challenge": "⚡ 挑战模式",
            "adventure": "🏰 闯关模式",
            "race": "🏁 竞速模式"
        }

        self.mode_title.setText(mode_titles.get(mode, "📚 经典模式"))

        # 根据模式设置不同参数
        if mode == "challenge":
            self.time_limit = 60  # 60秒挑战
            self.challenge_questions = 0
        elif mode == "race":
            self.time_limit = 30  # 30秒竞速
        elif mode == "adventure":
            # 闯关模式根据等级调整难度
            level_info = self.level_system.get_level_info()
            self.difficulty_slider.setValue(min(5, (level_info['level'] - 1) // 5 + 1))

        self.stacked_widget.setCurrentWidget(self.game_widget)

    def return_to_menu(self):
        """返回主菜单"""
        self.game_timer.stop()
        self.stacked_widget.setCurrentWidget(self.mode_widget)
        self.reset_game_state()

    def reset_game_state(self):
        """重置游戏状态"""
        self.start_btn.setEnabled(True)
        self.check_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.hint_btn.setEnabled(False)
        self.problem_label.setText("准备开始...")
        self.result_label.clear()
        self.answer_input.clear()

    def start_game(self):
        """开始游戏"""
        self.start_btn.setEnabled(False)
        self.check_btn.setEnabled(True)
        self.hint_btn.setEnabled(True)

        # 开始计时
        if self.current_mode in ["challenge", "race"]:
            self.game_timer.start(1000)  # 每秒更新

        self.generate_new_problem()

    def generate_new_problem(self):
        """生成新题目"""
        decimal_places = self.decimal_places_spinbox.value()
        difficulty = self.difficulty_slider.value()

        # 根据难度调整数值范围
        ranges = {
            1: (0.1, 2.9),    # 简单
            2: (0.1, 4.9),    # 较简单
            3: (0.1, 9.9),    # 中等
            4: (0.1, 19.9),   # 较难
            5: (0.1, 99.9)    # 困难
        }

        min_val, max_val = ranges[difficulty]

        self.current_a = round(random.uniform(min_val, max_val), decimal_places)
        self.current_b = round(random.uniform(min_val, max_val), decimal_places)

        # 获取选中的运算符
        operators = self.get_selected_operators()
        self.current_op = random.choice(operators)

        # 确保结果合理
        self.current_a, self.current_b = self.ensure_positive_result(
            self.current_a, self.current_b, self.current_op)

        # 计算正确答案
        self.correct_answer = self.calculate_answer(
            self.current_a, self.current_b, self.current_op)

        # 显示题目
        problem_text = f"{self.current_a} {self.current_op} {self.current_b} = ?"
        self.problem_label.setText(problem_text)

        # 记录开始时间
        self.start_time = time.time()

        # 清空输入和结果
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()

        # 启用下一题按钮
        self.next_btn.setEnabled(True)

    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_checkbox.isChecked():
            operators.append('+')
        if self.subtract_checkbox.isChecked():
            operators.append('-')
        if self.multiply_checkbox.isChecked():
            operators.append('×')
        if self.divide_checkbox.isChecked():
            operators.append('÷')

        return operators if operators else ['+']

    def ensure_positive_result(self, a, b, op):
        """确保减法结果为正数，除法结果合理"""
        if op == '-' and a < b:
            return b, a
        elif op == '÷':
            if b > a:
                a, b = b, a
            # 确保除法结果不会太复杂
            decimal_places = self.decimal_places_spinbox.value()
            result = round(a / b, decimal_places)
            b = round(a / result, decimal_places)
        return a, b

    def calculate_answer(self, a, b, op):
        """计算正确答案"""
        if op == '+':
            return a + b
        elif op == '-':
            return a - b
        elif op == '×':
            return a * b
        elif op == '÷':
            return a / b
        return 0

    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            decimal_places = self.decimal_places_spinbox.value()
            correct_answer = round(self.correct_answer, decimal_places)

            # 计算答题时间
            answer_time = time.time() - self.start_time
            self.total_time += answer_time

            self.total_questions += 1

            if abs(user_answer - correct_answer) < 0.001:
                # 答对了
                self.score += self.calculate_score(answer_time)
                self.streak += 1
                self.max_streak = max(self.max_streak, self.streak)

                # 更新最佳时间
                if answer_time < self.best_time:
                    self.best_time = answer_time

                # 添加经验值
                exp_gained = self.calculate_exp(answer_time)
                old_level = self.level_system.level
                new_level = self.level_system.add_exp(exp_gained)

                # 显示结果
                self.show_correct_result(correct_answer, answer_time, exp_gained)

                # 检查升级
                if new_level > old_level:
                    self.show_level_up(new_level)

                # 检查成就
                self.check_achievements(answer_time)

            else:
                # 答错了
                self.streak = 0
                self.show_wrong_result(correct_answer)

            # 更新显示
            self.update_status_display()

            # 在挑战模式下增加题目计数
            if self.current_mode == "challenge":
                self.challenge_questions += 1

        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

    def calculate_score(self, answer_time):
        """计算得分"""
        base_score = 10
        difficulty_bonus = self.difficulty_slider.value() * 5
        speed_bonus = max(0, int((5 - answer_time) * 2))  # 速度奖励
        streak_bonus = min(self.streak * 2, 50)  # 连击奖励，最多50分

        return base_score + difficulty_bonus + speed_bonus + streak_bonus

    def calculate_exp(self, answer_time):
        """计算经验值"""
        base_exp = 5
        difficulty_bonus = self.difficulty_slider.value() * 2
        speed_bonus = max(0, int((3 - answer_time) * 3))

        return base_exp + difficulty_bonus + speed_bonus

    def show_correct_result(self, correct_answer, answer_time, exp_gained):
        """显示正确答案的结果"""
        time_text = f"{answer_time:.1f}秒"
        if answer_time < 3:
            time_text += " ⚡闪电般的速度！"
        elif answer_time < 5:
            time_text += " 🚀很快！"

        result_text = f"🎉 正确！答案是 {correct_answer}\n⏱️ 用时: {time_text}\n✨ 获得经验: +{exp_gained}"

        self.result_label.setText(result_text)
        self.result_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F5E8, stop:1 #C8E6C9);
                border: 2px solid #4CAF50;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
            }
        """)

        # 播放成功动画
        self.animate_success()

    def show_wrong_result(self, correct_answer):
        """显示错误答案的结果"""
        self.result_label.setText(f"❌ 错误！正确答案是 {correct_answer}\n💪 继续加油！")
        self.result_label.setStyleSheet("""
            QLabel {
                color: #F44336;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFEBEE, stop:1 #FFCDD2);
                border: 2px solid #F44336;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
            }
        """)

    def show_level_up(self, new_level):
        """显示升级提示"""
        QMessageBox.information(
            self,
            "🎉 恭喜升级！",
            f"🏆 恭喜！你已升级到 {new_level} 级！\n"
            f"🌟 解锁了更多功能和挑战！\n"
            f"💪 继续努力，成为数学大师！"
        )

    def check_achievements(self, answer_time):
        """检查成就"""
        achievements_unlocked = []

        # 检查各种成就
        if self.score > 0:
            achievement = self.achievement_system.check_achievement('first_correct', True)
            if achievement:
                achievements_unlocked.append(achievement)

        if self.streak >= 5:
            achievement = self.achievement_system.check_achievement('streak_5', True)
            if achievement:
                achievements_unlocked.append(achievement)

        if self.streak >= 10:
            achievement = self.achievement_system.check_achievement('streak_10', True)
            if achievement:
                achievements_unlocked.append(achievement)

        if self.streak >= 20:
            achievement = self.achievement_system.check_achievement('streak_20', True)
            if achievement:
                achievements_unlocked.append(achievement)

        if answer_time < 3:
            achievement = self.achievement_system.check_achievement('speed_demon', True)
            if achievement:
                achievements_unlocked.append(achievement)

        if self.score >= 1000:  # 假设100题大约能得1000分
            achievement = self.achievement_system.check_achievement('century', True)
            if achievement:
                achievements_unlocked.append(achievement)

        accuracy = (self.score / max(self.total_questions * 10, 1)) * 100
        if accuracy >= 95 and self.total_questions >= 20:
            achievement = self.achievement_system.check_achievement('perfectionist', True)
            if achievement:
                achievements_unlocked.append(achievement)

        # 检查是否掌握所有运算符
        all_ops = [self.add_checkbox.isChecked(), self.subtract_checkbox.isChecked(),
                   self.multiply_checkbox.isChecked(), self.divide_checkbox.isChecked()]
        if all(all_ops):
            achievement = self.achievement_system.check_achievement('math_wizard', True)
            if achievement:
                achievements_unlocked.append(achievement)

        # 显示解锁的成就
        for achievement in achievements_unlocked:
            self.show_achievement_unlocked(achievement)

    def show_achievement_unlocked(self, achievement):
        """显示成就解锁"""
        QMessageBox.information(
            self,
            "🏆 成就解锁！",
            f"🎉 恭喜解锁成就：\n\n"
            f"{achievement['name']}\n"
            f"{achievement['desc']}\n\n"
            f"🌟 继续努力，解锁更多成就！"
        )

    def next_question(self):
        """下一题"""
        self.generate_new_problem()

    def show_hint(self):
        """显示提示"""
        decimal_places = self.decimal_places_spinbox.value()
        correct_answer = round(self.correct_answer, decimal_places)

        # 根据难度给出不同程度的提示
        difficulty = self.difficulty_slider.value()

        if difficulty <= 2:
            # 简单模式：给出计算步骤
            if self.current_op == '+':
                hint = f"💡 提示：{self.current_a} + {self.current_b}\n把两个数相加就可以了！"
            elif self.current_op == '-':
                hint = f"💡 提示：{self.current_a} - {self.current_b}\n用大数减去小数！"
            elif self.current_op == '×':
                hint = f"💡 提示：{self.current_a} × {self.current_b}\n两个数相乘！"
            else:
                hint = f"💡 提示：{self.current_a} ÷ {self.current_b}\n第一个数除以第二个数！"
        else:
            # 困难模式：只给出答案的一部分
            answer_str = str(correct_answer)
            if len(answer_str) > 1:
                hint_answer = answer_str[0] + "?" * (len(answer_str) - 1)
                hint = f"💡 提示：答案开头是 {answer_str[0]}\n完整答案：{hint_answer}"
            else:
                hint = f"💡 提示：答案是一位数！"

        QMessageBox.information(self, "💡 智能提示", hint)

    def update_timer(self):
        """更新计时器"""
        if self.current_mode == "challenge":
            self.time_limit -= 1
            minutes = self.time_limit // 60
            seconds = self.time_limit % 60
            self.time_label.setText(f"⏱️ 剩余: {minutes:02d}:{seconds:02d}")

            if self.time_limit <= 0:
                self.end_challenge_mode()
        elif self.current_mode == "race":
            self.time_limit -= 1
            if self.time_limit <= 0:
                self.end_race_mode()
        else:
            # 普通计时
            elapsed = int(time.time() - self.start_time) if hasattr(self, 'start_time') else 0
            minutes = elapsed // 60
            seconds = elapsed % 60
            self.time_label.setText(f"⏱️ 时间: {minutes:02d}:{seconds:02d}")

    def end_challenge_mode(self):
        """结束挑战模式"""
        self.game_timer.stop()
        QMessageBox.information(
            self,
            "⏰ 挑战结束！",
            f"🎯 挑战模式结束！\n\n"
            f"📊 统计数据：\n"
            f"✅ 答对题数: {self.challenge_questions}\n"
            f"💎 总得分: {self.score}\n"
            f"🔥 最高连击: {self.max_streak}\n"
            f"⚡ 平均用时: {self.total_time/max(self.total_questions,1):.1f}秒\n\n"
            f"🏆 表现{'优秀' if self.challenge_questions >= 15 else '良好' if self.challenge_questions >= 10 else '需要加油'}！"
        )
        self.return_to_menu()

    def end_race_mode(self):
        """结束竞速模式"""
        self.game_timer.stop()
        QMessageBox.information(
            self,
            "🏁 竞速结束！",
            f"🏁 竞速模式结束！\n\n"
            f"📊 最终成绩：\n"
            f"✅ 答对题数: {self.total_questions}\n"
            f"💎 总得分: {self.score}\n"
            f"⚡ 最快用时: {self.best_time:.1f}秒\n\n"
            f"🏆 {'速度惊人！' if self.total_questions >= 20 else '表现不错！' if self.total_questions >= 15 else '继续练习！'}"
        )
        self.return_to_menu()

    def update_status_display(self):
        """更新状态显示"""
        # 更新等级和经验
        level_info = self.level_system.get_level_info()
        self.level_label.setText(f"🏆 等级: {level_info['level']}")
        self.exp_bar.setValue(int(level_info['progress']))
        self.exp_bar.setFormat(f"{level_info['exp']}/{level_info['exp_to_next']} EXP")

        # 更新分数和连击
        self.score_label.setText(f"💎 得分: {self.score}")
        self.streak_label.setText(f"🔥 连击: {self.streak}")

    def update_difficulty_display(self):
        """更新难度显示"""
        difficulty_names = {
            1: "🌟 简单",
            2: "🌟🌟 较简单",
            3: "🌟🌟🌟 中等",
            4: "🌟🌟🌟🌟 较难",
            5: "🌟🌟🌟🌟🌟 困难"
        }
        difficulty = self.difficulty_slider.value()
        self.difficulty_label.setText(f"难度: {difficulty_names.get(difficulty, '🌟 简单')}")

    def animate_success(self):
        """成功动画效果"""
        # 简单的颜色动画效果
        self.problem_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F5E8, stop:1 #4CAF50);
                border-radius: 20px;
                padding: 30px;
            }
        """)

        # 1秒后恢复原样
        QTimer.singleShot(1000, self.restore_problem_style)

    def restore_problem_style(self):
        """恢复题目样式"""
        self.problem_label.setStyleSheet("color: white; padding: 30px;")

    def apply_game_style(self):
        """应用游戏样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
            QWidget {
                font-family: "Microsoft YaHei";
            }
        """)

    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                border: none;
                border-radius: 10px;
                color: white;
                font: bold 14px "Microsoft YaHei";
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(color)}, stop:1 {color});
                transform: scale(1.02);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.darken_color(color)}, stop:1 {self.darken_color(color, 0.3)});
            }}
            QPushButton:disabled {{
                background: #CCCCCC;
                color: #666666;
            }}
        """

    def get_spinbox_style(self):
        """获取数字输入框样式"""
        return """
            QSpinBox {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
                min-width: 80px;
            }
            QSpinBox:focus {
                border: 2px solid #4A90E2;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                border: none;
                background: #4A90E2;
                color: white;
                font-weight: bold;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background: #357ABD;
            }
        """

    def get_slider_style(self):
        """获取滑块样式"""
        return """
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #b4b4b4, stop:1 #8f8f8f);
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #d4d4d4, stop:1 #afafaf);
            }
        """

    def get_checkbox_style(self):
        """获取复选框样式"""
        return """
            QCheckBox {
                font: 12px "Microsoft YaHei";
                color: #333;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #CCCCCC;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4A90E2;
                border: 2px solid #4A90E2;
            }
            QCheckBox::indicator:checked::after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
            QCheckBox:hover {
                color: #4A90E2;
            }
        """

    def lighten_color(self, color, factor=0.2):
        """颜色变亮"""
        if color.startswith('#'):
            # 简化的颜色处理
            return color.replace('4', '6').replace('2', '4').replace('A', 'C')
        return color

    def darken_color(self, color, factor=0.2):
        """颜色变暗"""
        if color.startswith('#'):
            # 简化的颜色处理
            return color.replace('C', 'A').replace('6', '4').replace('4', '2')
        return color

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用图标和信息
    app.setApplicationName("数学大冒险")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Math Game Studio")

    # 创建并显示游戏窗口
    game = GameCalculator()

    # 运行应用
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
