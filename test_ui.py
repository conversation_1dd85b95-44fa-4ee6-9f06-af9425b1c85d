import tkinter as tk
from tkinter import messagebox
import random

class TestUI:
    def __init__(self):
        self.root = tk.Tk()
        self.correct_answer = 0
        self.setup_ui()
        self.generate_new_problem()
    
    def setup_ui(self):
        self.root.title('🧮 小数运算练习器 - 测试版')
        self.root.geometry('800x700')
        self.root.configure(bg='#f0f8ff')
        
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f8ff', padx=30, pady=30)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text='🧮 小数运算练习器',
            font=('Arial', 28, 'bold'),
            fg='#2c3e50',
            bg='#f0f8ff',
            pady=20
        )
        title_label.pack()
        
        # 设置区域
        settings_frame = tk.LabelFrame(
            main_frame,
            text='⚙️ 设置',
            font=('Arial', 16, 'bold'),
            fg='#34495e',
            bg='#ecf0f1',
            padx=20,
            pady=15
        )
        settings_frame.pack(fill='x', pady=15)
        
        # 小数位数
        decimal_frame = tk.Frame(settings_frame, bg='#ecf0f1')
        decimal_frame.pack(fill='x', pady=8)
        
        tk.Label(
            decimal_frame,
            text='小数位数:',
            font=('Arial', 14, 'bold'),
            bg='#ecf0f1'
        ).pack(side='left')
        
        self.decimal_var = tk.IntVar(value=1)
        decimal_spinbox = tk.Spinbox(
            decimal_frame,
            from_=1,
            to=3,
            textvariable=self.decimal_var,
            font=('Arial', 14),
            width=8,
            command=self.generate_new_problem
        )
        decimal_spinbox.pack(side='left', padx=15)
        
        # 运算符选择
        operator_frame = tk.Frame(settings_frame, bg='#ecf0f1')
        operator_frame.pack(fill='x', pady=8)
        
        tk.Label(
            operator_frame,
            text='运算符:',
            font=('Arial', 14, 'bold'),
            bg='#ecf0f1'
        ).pack(side='left')
        
        self.add_var = tk.BooleanVar(value=True)
        self.subtract_var = tk.BooleanVar(value=True)
        self.multiply_var = tk.BooleanVar(value=False)
        self.divide_var = tk.BooleanVar(value=False)
        
        operators = [
            ('➕ 加法', self.add_var),
            ('➖ 减法', self.subtract_var),
            ('✖️ 乘法', self.multiply_var),
            ('➗ 除法', self.divide_var)
        ]
        
        for text, var in operators:
            cb = tk.Checkbutton(
                operator_frame,
                text=text,
                variable=var,
                font=('Arial', 12),
                bg='#ecf0f1',
                command=self.generate_new_problem
            )
            cb.pack(side='left', padx=12)
        
        # 题目显示区域
        problem_frame = tk.LabelFrame(
            main_frame,
            text='📝 题目',
            font=('Arial', 16, 'bold'),
            fg='#27ae60',
            bg='#d5f4e6',
            padx=20,
            pady=15
        )
        problem_frame.pack(fill='x', pady=15)
        
        # 题目标签
        self.problem_label = tk.Label(
            problem_frame,
            text='',
            font=('Arial', 36, 'bold'),
            fg='#2d8659',
            bg='#ffffff',
            pady=25,
            relief='ridge',
            borderwidth=3
        )
        self.problem_label.pack(fill='x', pady=12)
        
        # 答案输入区域
        answer_frame = tk.Frame(problem_frame, bg='#d5f4e6')
        answer_frame.pack(fill='x', pady=12)
        
        tk.Label(
            answer_frame,
            text='💡 你的答案:',
            font=('Arial', 16, 'bold'),
            bg='#d5f4e6'
        ).pack(side='left')
        
        self.answer_var = tk.StringVar()
        self.answer_entry = tk.Entry(
            answer_frame,
            textvariable=self.answer_var,
            font=('Arial', 20),
            width=12,
            justify='center',
            relief='solid',
            borderwidth=2
        )
        self.answer_entry.pack(side='left', padx=15, fill='x', expand=True)
        self.answer_entry.bind('<Return>', lambda e: self.check_answer())
        
        # 按钮区域
        button_frame = tk.LabelFrame(
            main_frame,
            text='🎮 操作',
            font=('Arial', 16, 'bold'),
            fg='#e67e22',
            bg='#fdeaa7',
            padx=20,
            pady=15
        )
        button_frame.pack(fill='x', pady=15)
        
        buttons_container = tk.Frame(button_frame, bg='#fdeaa7')
        buttons_container.pack(fill='x')
        
        # 检查答案按钮
        check_button = tk.Button(
            buttons_container,
            text='✅ 检查答案',
            font=('Arial', 16, 'bold'),
            bg='#27ae60',
            fg='white',
            padx=25,
            pady=12,
            command=self.check_answer,
            relief='raised',
            borderwidth=3
        )
        check_button.pack(side='left', padx=8, fill='x', expand=True)
        
        # 下一题按钮
        next_button = tk.Button(
            buttons_container,
            text='🔄 下一题',
            font=('Arial', 16, 'bold'),
            bg='#3498db',
            fg='white',
            padx=25,
            pady=12,
            command=self.generate_new_problem,
            relief='raised',
            borderwidth=3
        )
        next_button.pack(side='left', padx=8, fill='x', expand=True)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(
            main_frame,
            text='📋 结果',
            font=('Arial', 16, 'bold'),
            fg='#8e44ad',
            bg='#f4ecf7',
            padx=20,
            pady=15
        )
        result_frame.pack(fill='x', pady=15)
        
        self.result_label = tk.Label(
            result_frame,
            text="点击'下一题'开始练习",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='#ffffff',
            pady=18,
            relief='solid',
            borderwidth=2
        )
        self.result_label.pack(fill='x', pady=10)
    
    def generate_decimal_numbers(self):
        """生成小数"""
        decimal_places = self.decimal_var.get()
        min_val = 0.1
        max_val = 9.9
        
        if decimal_places == 2:
            max_val = 9.99
        elif decimal_places == 3:
            max_val = 9.999
        
        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        
        return a, b
    
    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_var.get():
            operators.append('+')
        if self.subtract_var.get():
            operators.append('-')
        if self.multiply_var.get():
            operators.append('×')
        if self.divide_var.get():
            operators.append('÷')
        
        if not operators:
            operators = ['+']
            self.add_var.set(True)
        
        return operators
    
    def generate_new_problem(self):
        """生成新题目"""
        a, b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        operator = random.choice(operators)
        
        # 确保减法结果为正数
        if operator == '-' and a < b:
            a, b = b, a
        
        # 确保除法结果合理
        if operator == '÷':
            if b == 0:
                b = 0.1
            if a < b:
                a, b = b, a
        
        # 计算正确答案
        if operator == '+':
            self.correct_answer = round(a + b, self.decimal_var.get())
        elif operator == '-':
            self.correct_answer = round(a - b, self.decimal_var.get())
        elif operator == '×':
            self.correct_answer = round(a * b, self.decimal_var.get())
        elif operator == '÷':
            self.correct_answer = round(a / b, self.decimal_var.get())
        
        # 显示题目
        self.problem_label.config(text=f"{a} {operator} {b} = ?")
        
        # 清空输入和结果
        self.answer_var.set('')
        self.result_label.config(text="请输入答案", bg='#ffffff', fg='#2c3e50')
        
        # 设置焦点
        self.answer_entry.focus_set()
    
    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_var.get())
            
            if abs(user_answer - self.correct_answer) < 0.001:
                # 正确
                self.result_label.config(
                    text=f"🎉 正确！答案是 {self.correct_answer}",
                    bg='#d5f4e6',
                    fg='#27ae60'
                )
            else:
                # 错误
                self.result_label.config(
                    text=f"❌ 错误！正确答案是 {self.correct_answer}",
                    bg='#fadbd8',
                    fg='#e74c3c'
                )
                
        except ValueError:
            messagebox.showwarning("输入错误", "请输入有效的数字！")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    app = TestUI()
    app.run()

if __name__ == '__main__':
    main()
