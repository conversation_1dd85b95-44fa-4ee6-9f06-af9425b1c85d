import sys
import random
import pandas as pd
from PySide2.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QLabel, QSpinBox, QCheckBox, QPushButton,
                               QLineEdit, QGroupBox, QGridLayout, QMessageBox, QFrame)
from PySide2.QtCore import Qt
from PySide2.QtGui import QFont

class OptimizedCalculator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.generate_new_problem()
    
    def initUI(self):
        self.setWindowTitle('🧮 小数运算练习器 - 优化版')
        self.setGeometry(200, 100, 900, 800)
        self.setMinimumSize(800, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel('🧮 小数运算练习器')
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 28px; 
                font-weight: bold; 
                color: #1976D2;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E3F2FD, stop:1 #BBDEFB);
                border-radius: 15px;
                border: 3px solid #90CAF9;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title)
        
        # 设置组
        settings_group = QGroupBox("⚙️ 游戏设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #1976D2;
                border: 3px solid #BBDEFB;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F3F9FF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
            }
        """)
        settings_layout = QGridLayout(settings_group)
        settings_layout.setSpacing(15)
        
        # 小数位数设置
        decimal_label = QLabel("小数位数:")
        decimal_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #424242;")
        settings_layout.addWidget(decimal_label, 0, 0)
        
        self.decimal_places_spinbox = QSpinBox()
        self.decimal_places_spinbox.setRange(1, 3)
        self.decimal_places_spinbox.setValue(1)
        self.decimal_places_spinbox.setStyleSheet("""
            QSpinBox {
                font-size: 16px;
                padding: 8px;
                border: 2px solid #BDBDBD;
                border-radius: 8px;
                background-color: white;
                min-width: 80px;
            }
            QSpinBox:focus {
                border: 2px solid #4CAF50;
            }
        """)
        self.decimal_places_spinbox.valueChanged.connect(self.generate_new_problem)
        settings_layout.addWidget(self.decimal_places_spinbox, 0, 1)
        
        # 运算符选择
        operator_label = QLabel("运算符:")
        operator_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #424242;")
        settings_layout.addWidget(operator_label, 1, 0)
        
        operators_layout = QHBoxLayout()
        operators_layout.setSpacing(20)
        
        self.add_checkbox = QCheckBox('➕ 加法')
        self.add_checkbox.setChecked(True)
        self.subtract_checkbox = QCheckBox('➖ 减法')
        self.subtract_checkbox.setChecked(True)
        self.multiply_checkbox = QCheckBox('✖️ 乘法')
        self.multiply_checkbox.setChecked(False)
        self.divide_checkbox = QCheckBox('➗ 除法')
        self.divide_checkbox.setChecked(False)
        
        checkbox_style = """
            QCheckBox {
                font-size: 14px;
                font-weight: bold;
                color: #424242;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid #BDBDBD;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
            }
        """
        
        for cb in [self.add_checkbox, self.subtract_checkbox, self.multiply_checkbox, self.divide_checkbox]:
            cb.setStyleSheet(checkbox_style)
            cb.stateChanged.connect(self.generate_new_problem)
            operators_layout.addWidget(cb)
        
        settings_layout.addLayout(operators_layout, 1, 1, 1, 2)
        main_layout.addWidget(settings_group)
        
        # 题目显示区域
        problem_group = QGroupBox("📝 当前题目")
        problem_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #388E3C;
                border: 3px solid #C8E6C9;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F1F8E9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
            }
        """)
        problem_layout = QVBoxLayout(problem_group)
        problem_layout.setSpacing(20)
        
        # 题目显示
        self.problem_label = QLabel()
        self.problem_label.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: #2E7D32;
                text-align: center;
                padding: 30px;
                background-color: white;
                border: 3px solid #81C784;
                border-radius: 15px;
                margin: 15px;
                min-height: 60px;
            }
        """)
        self.problem_label.setAlignment(Qt.AlignCenter)
        problem_layout.addWidget(self.problem_label)
        
        # 答案输入区域
        answer_frame = QFrame()
        answer_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 3px solid #E0E0E0;
                border-radius: 12px;
                padding: 20px;
                margin: 15px;
            }
        """)
        answer_layout = QHBoxLayout(answer_frame)
        answer_layout.setSpacing(15)
        
        answer_label = QLabel("💡 你的答案:")
        answer_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #424242;
            }
        """)
        answer_layout.addWidget(answer_label)
        
        self.answer_input = QLineEdit()
        self.answer_input.setStyleSheet("""
            QLineEdit {
                font-size: 20px;
                padding: 15px;
                border: 3px solid #BDBDBD;
                border-radius: 10px;
                background-color: #FAFAFA;
                min-height: 25px;
            }
            QLineEdit:focus {
                border: 3px solid #4CAF50;
                background-color: white;
            }
        """)
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)
        
        problem_layout.addWidget(answer_frame)
        main_layout.addWidget(problem_group)
        
        # 按钮区域
        button_group = QGroupBox("🎮 操作按钮")
        button_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #FF6F00;
                border: 3px solid #FFE0B2;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #FFF8E1;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
            }
        """)
        button_layout = QHBoxLayout(button_group)
        button_layout.setSpacing(20)

        # 按钮样式
        button_style = """
            QPushButton {
                font-size: 18px;
                font-weight: bold;
                padding: 18px 30px;
                border-radius: 12px;
                border: none;
                min-height: 30px;
                min-width: 160px;
            }
            QPushButton:hover {
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
        """

        self.check_button = QPushButton("✅ 检查答案")
        self.check_button.setStyleSheet(button_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
        """)
        self.check_button.clicked.connect(self.check_answer)
        button_layout.addWidget(self.check_button)

        self.next_button = QPushButton("🔄 下一题")
        self.next_button.setStyleSheet(button_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42A5F5, stop:1 #2196F3);
            }
        """)
        self.next_button.clicked.connect(self.generate_new_problem)
        button_layout.addWidget(self.next_button)

        main_layout.addWidget(button_group)

        # Excel生成按钮
        excel_group = QGroupBox("📊 Excel生成")
        excel_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #388E3C;
                border: 3px solid #C8E6C9;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F1F8E9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
            }
        """)
        excel_layout = QHBoxLayout(excel_group)
        
        self.generate_excel_button = QPushButton("📊 生成1000题Excel文件")
        self.generate_excel_button.setStyleSheet(button_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF9800, stop:1 #F57C00);
                color: white;
                min-width: 250px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFB74D, stop:1 #FF9800);
            }
        """)
        self.generate_excel_button.clicked.connect(self.generate_excel)
        excel_layout.addWidget(self.generate_excel_button)

        main_layout.addWidget(excel_group)
        
        # 结果显示区域
        result_group = QGroupBox("📋 答题结果")
        result_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #7B1FA2;
                border: 3px solid #E1BEE7;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F3E5F5;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
            }
        """)
        result_layout = QVBoxLayout(result_group)
        
        self.result_label = QLabel("点击'下一题'开始练习")
        self.result_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #424242;
                text-align: center;
                padding: 20px;
                background-color: white;
                border: 3px solid #E0E0E0;
                border-radius: 10px;
                margin: 15px;
                min-height: 40px;
            }
        """)
        self.result_label.setAlignment(Qt.AlignCenter)
        result_layout.addWidget(self.result_label)
        
        main_layout.addWidget(result_group)
        
        # 添加弹性空间
        main_layout.addStretch()
    
    def generate_decimal_numbers(self):
        """根据设置生成两个小数"""
        decimal_places = self.decimal_places_spinbox.value()
        min_val = 0.1
        max_val = 9.9
        
        # 根据小数位数调整范围
        if decimal_places == 2:
            max_val = 9.99
        elif decimal_places == 3:
            max_val = 9.999
        
        a = round(random.uniform(min_val, max_val), decimal_places)
        b = round(random.uniform(min_val, max_val), decimal_places)
        
        return a, b

    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_checkbox.isChecked():
            operators.append('+')
        if self.subtract_checkbox.isChecked():
            operators.append('-')
        if self.multiply_checkbox.isChecked():
            operators.append('×')
        if self.divide_checkbox.isChecked():
            operators.append('÷')

        # 如果没有选择任何运算符，默认使用加法
        if not operators:
            operators = ['+']
            self.add_checkbox.setChecked(True)

        return operators

    def generate_new_problem(self):
        """生成新题目"""
        a, b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        operator = random.choice(operators)

        # 确保减法结果为正数
        if operator == '-' and a < b:
            a, b = b, a

        # 确保除法结果合理
        if operator == '÷':
            # 确保除数不为0且结果不会太复杂
            if b == 0:
                b = 0.1
            # 如果被除数小于除数，交换位置
            if a < b:
                a, b = b, a

        # 计算正确答案
        if operator == '+':
            self.correct_answer = round(a + b, self.decimal_places_spinbox.value())
        elif operator == '-':
            self.correct_answer = round(a - b, self.decimal_places_spinbox.value())
        elif operator == '×':
            self.correct_answer = round(a * b, self.decimal_places_spinbox.value())
        elif operator == '÷':
            self.correct_answer = round(a / b, self.decimal_places_spinbox.value())

        # 显示题目
        self.problem_label.setText(f"{a} {operator} {b} = ?")

        # 清空输入框和结果
        self.answer_input.clear()
        self.result_label.setText("请输入答案")
        self.result_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #424242;
                text-align: center;
                padding: 20px;
                background-color: white;
                border: 3px solid #E0E0E0;
                border-radius: 10px;
                margin: 15px;
                min-height: 40px;
            }
        """)

        # 设置焦点到输入框
        self.answer_input.setFocus()

    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())

            if abs(user_answer - self.correct_answer) < 0.001:
                # 答对了
                self.result_label.setText(f"🎉 正确！答案是 {self.correct_answer}")
                self.result_label.setStyleSheet("""
                    QLabel {
                        font-size: 18px;
                        font-weight: bold;
                        color: #2E7D32;
                        text-align: center;
                        padding: 20px;
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #E8F5E8, stop:1 #C8E6C9);
                        border: 3px solid #4CAF50;
                        border-radius: 10px;
                        margin: 15px;
                        min-height: 40px;
                    }
                """)
            else:
                # 答错了
                self.result_label.setText(f"❌ 错误！正确答案是 {self.correct_answer}")
                self.result_label.setStyleSheet("""
                    QLabel {
                        font-size: 18px;
                        font-weight: bold;
                        color: #C62828;
                        text-align: center;
                        padding: 20px;
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #FFEBEE, stop:1 #FFCDD2);
                        border: 3px solid #F44336;
                        border-radius: 10px;
                        margin: 15px;
                        min-height: 40px;
                    }
                """)

        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")

    def generate_problem_data(self):
        """生成单个题目数据（用于Excel）"""
        a, b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        operator = random.choice(operators)

        # 确保减法结果为正数
        if operator == '-' and a < b:
            a, b = b, a

        # 确保除法结果合理
        if operator == '÷':
            if b == 0:
                b = 0.1
            if a < b:
                a, b = b, a

        # 计算结果
        if operator == '+':
            result = round(a + b, self.decimal_places_spinbox.value())
        elif operator == '-':
            result = round(a - b, self.decimal_places_spinbox.value())
        elif operator == '×':
            result = round(a * b, self.decimal_places_spinbox.value())
        elif operator == '÷':
            result = round(a / b, self.decimal_places_spinbox.value())

        return [a, operator, b, '=', result]

    def generate_excel(self):
        """生成Excel文件"""
        try:
            # 生成1000道题目
            problems_data = []
            for i in range(1000):
                problem = self.generate_problem_data()
                problems_data.append(problem)

            # 创建一个包含所有列的字典，每列都有20行
            final_data = {}
            num_rows = 20  # 每列20行

            # 为50组题目创建列
            for group in range(50):
                start_idx = group * num_rows
                end_idx = start_idx + num_rows
                group_problems = problems_data[start_idx:end_idx]

                # 为每组创建5列（数字A, 运算符, 数字B, 等号, 结果）
                group_name = f"第{group+1}组"
                final_data[f"{group_name}_数字A"] = [problem[0] for problem in group_problems]
                final_data[f"{group_name}_运算符"] = [problem[1] for problem in group_problems]
                final_data[f"{group_name}_数字B"] = [problem[2] for problem in group_problems]
                final_data[f"{group_name}_等号"] = [problem[3] for problem in group_problems]
                final_data[f"{group_name}_结果"] = [problem[4] for problem in group_problems]

            # 创建DataFrame并保存
            df = pd.DataFrame(final_data)

            decimal_places = self.decimal_places_spinbox.value()
            filename = f'小数运算练习_{decimal_places}位小数_1000题.xlsx'
            df.to_excel(filename, index=False)

            QMessageBox.information(
                self,
                "生成成功",
                f"✅ Excel文件生成成功！\n\n"
                f"📁 文件名: {filename}\n"
                f"📊 格式: 每个题目占用5个单元格\n"
                f"📋 结构: 数字A | 运算符 | 数字B | 等号 | 结果\n"
                f"📈 总计: 1000题，分50组，每组20题\n\n"
                f"🎉 文件已保存到当前目录！"
            )

        except Exception as e:
            QMessageBox.critical(self, "生成失败", f"生成Excel文件时出错：\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("小数运算练习器")
    app.setApplicationVersion("1.0")

    calculator = OptimizedCalculator()
    calculator.show()

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
