import random
import pandas as pd

print("开始测试...")

# 生成简单的测试数据
data = []
for i in range(20):
    a = round(random.uniform(0.1, 9.9), 1)
    b = round(random.uniform(0.1, 9.9), 1)
    op = random.choice(['+', '-'])
    if op == '-' and a < b:
        a, b = b, a
    data.append([a, op, b, '='])

# 创建DataFrame
df = pd.DataFrame(data, columns=['第一个数', '运算符', '第二个数', '等号'])

# 保存到Excel
filename = '简单测试.xlsx'
df.to_excel(filename, index=False)

print(f"测试成功！生成了{len(data)}道题目")
print(f"文件保存为：{filename}")
print("前5行预览：")
print(df.head())
