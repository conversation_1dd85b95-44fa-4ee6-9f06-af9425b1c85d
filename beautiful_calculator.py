import sys
import random
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QSpinBox, QCheckBox, QPushButton, 
                             QLineEdit, QGroupBox, QGridLayout, QMessageBox,
                             QFileDialog, QProgressBar, QTabWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class BeautifulCalculator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_a = 0
        self.current_b = 0
        self.current_op = '+'
        self.correct_answer = 0
        self.score = 0
        self.total_questions = 0
        
        self.initUI()
        self.apply_beautiful_style()
        self.generate_new_problem()
    
    def initUI(self):
        self.setWindowTitle('🧮 美化版小数运算练习器')
        self.setGeometry(200, 100, 700, 600)
        
        # 创建中央widget和标签页
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 练习标签页
        self.practice_tab = QWidget()
        self.tab_widget.addTab(self.practice_tab, "🎯 练习模式")
        self.setup_practice_tab()
        
        # Excel生成标签页
        self.excel_tab = QWidget()
        self.tab_widget.addTab(self.excel_tab, "📊 Excel生成")
        self.setup_excel_tab()
    
    def setup_practice_tab(self):
        """设置练习标签页"""
        layout = QVBoxLayout(self.practice_tab)
        
        # 状态显示
        status_layout = QHBoxLayout()
        self.score_label = QLabel("得分: 0")
        self.accuracy_label = QLabel("正确率: 0%")
        status_layout.addWidget(self.score_label)
        status_layout.addWidget(self.accuracy_label)
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        # 设置区域
        settings_group = QGroupBox("⚙️ 设置")
        settings_layout = QGridLayout(settings_group)
        
        # 小数位数
        settings_layout.addWidget(QLabel("小数位数:"), 0, 0)
        self.decimal_places_spinbox = QSpinBox()
        self.decimal_places_spinbox.setRange(1, 3)
        self.decimal_places_spinbox.setValue(1)
        self.decimal_places_spinbox.valueChanged.connect(self.generate_new_problem)
        settings_layout.addWidget(self.decimal_places_spinbox, 0, 1)
        
        # 运算符选择
        settings_layout.addWidget(QLabel("运算符:"), 1, 0)
        operators_layout = QHBoxLayout()
        
        self.add_checkbox = QCheckBox('➕ 加法')
        self.add_checkbox.setChecked(True)
        self.subtract_checkbox = QCheckBox('➖ 减法')
        self.subtract_checkbox.setChecked(True)
        self.multiply_checkbox = QCheckBox('✖️ 乘法')
        self.divide_checkbox = QCheckBox('➗ 除法')
        
        for checkbox in [self.add_checkbox, self.subtract_checkbox, 
                        self.multiply_checkbox, self.divide_checkbox]:
            checkbox.stateChanged.connect(self.generate_new_problem)
            operators_layout.addWidget(checkbox)
        
        settings_layout.addLayout(operators_layout, 1, 1)
        layout.addWidget(settings_group)
        
        # 题目显示
        problem_group = QGroupBox("📝 题目")
        problem_layout = QVBoxLayout(problem_group)
        
        self.problem_label = QLabel()
        self.problem_label.setFont(QFont("Microsoft YaHei", 28, QFont.Bold))
        self.problem_label.setAlignment(Qt.AlignCenter)
        self.problem_label.setMinimumHeight(100)
        problem_layout.addWidget(self.problem_label)
        
        # 答案输入
        answer_layout = QHBoxLayout()
        answer_layout.addWidget(QLabel("💡 答案:"))
        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont("Microsoft YaHei", 16))
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)
        problem_layout.addLayout(answer_layout)
        
        layout.addWidget(problem_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.check_button = QPushButton("✅ 检查答案")
        self.check_button.clicked.connect(self.check_answer)
        button_layout.addWidget(self.check_button)
        
        self.next_button = QPushButton("⏭️ 下一题")
        self.next_button.clicked.connect(self.generate_new_problem)
        button_layout.addWidget(self.next_button)
        
        layout.addLayout(button_layout)
        
        # 结果显示
        self.result_label = QLabel()
        self.result_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumHeight(50)
        layout.addWidget(self.result_label)
    
    def setup_excel_tab(self):
        """设置Excel生成标签页"""
        layout = QVBoxLayout(self.excel_tab)
        
        # 标题
        title = QLabel("📊 批量生成Excel练习题")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 设置
        excel_group = QGroupBox("🔧 生成设置")
        excel_layout = QGridLayout(excel_group)
        
        # 题目数量
        excel_layout.addWidget(QLabel("题目数量:"), 0, 0)
        self.total_problems_spinbox = QSpinBox()
        self.total_problems_spinbox.setRange(100, 5000)
        self.total_problems_spinbox.setValue(1000)
        excel_layout.addWidget(self.total_problems_spinbox, 0, 1)
        
        # 每组题数
        excel_layout.addWidget(QLabel("每组题数:"), 0, 2)
        self.group_size_spinbox = QSpinBox()
        self.group_size_spinbox.setRange(10, 50)
        self.group_size_spinbox.setValue(20)
        excel_layout.addWidget(self.group_size_spinbox, 0, 3)
        
        # Excel小数位数
        excel_layout.addWidget(QLabel("小数位数:"), 1, 0)
        self.excel_decimal_places_spinbox = QSpinBox()
        self.excel_decimal_places_spinbox.setRange(1, 3)
        self.excel_decimal_places_spinbox.setValue(1)
        excel_layout.addWidget(self.excel_decimal_places_spinbox, 1, 1)
        
        # Excel运算符
        excel_layout.addWidget(QLabel("运算符:"), 1, 2)
        excel_ops_layout = QHBoxLayout()
        
        self.excel_add_cb = QCheckBox('➕')
        self.excel_add_cb.setChecked(True)
        self.excel_sub_cb = QCheckBox('➖')
        self.excel_sub_cb.setChecked(True)
        self.excel_mul_cb = QCheckBox('✖️')
        self.excel_div_cb = QCheckBox('➗')
        
        for cb in [self.excel_add_cb, self.excel_sub_cb, self.excel_mul_cb, self.excel_div_cb]:
            excel_ops_layout.addWidget(cb)
        
        excel_layout.addLayout(excel_ops_layout, 1, 3)
        layout.addWidget(excel_group)
        
        # 生成按钮
        self.generate_excel_button = QPushButton("📊 生成Excel文件")
        self.generate_excel_button.setMinimumHeight(50)
        self.generate_excel_button.clicked.connect(self.generate_excel)
        layout.addWidget(self.generate_excel_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
    
    def apply_beautiful_style(self):
        """应用美化样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font: bold 12px "Microsoft YaHei";
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #333;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font: bold 11px "Microsoft YaHei";
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5BA0F2, stop:1 #4A8ACD);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3A80D2, stop:1 #2A6AAD);
            }
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #4A90E2;
            }
            QSpinBox {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
                color: #4A90E2;
            }
            QLabel {
                color: #333;
            }
        """)
    
    def generate_decimal_numbers(self):
        """生成小数"""
        decimal_places = self.decimal_places_spinbox.value()
        max_val = 9.9 if decimal_places == 1 else (9.99 if decimal_places == 2 else 9.999)
        
        a = round(random.uniform(0.1, max_val), decimal_places)
        b = round(random.uniform(0.1, max_val), decimal_places)
        return a, b
    
    def get_selected_operators(self):
        """获取选中的运算符"""
        operators = []
        if self.add_checkbox.isChecked():
            operators.append('+')
        if self.subtract_checkbox.isChecked():
            operators.append('-')
        if self.multiply_checkbox.isChecked():
            operators.append('×')
        if self.divide_checkbox.isChecked():
            operators.append('÷')
        
        return operators if operators else ['+']
    
    def generate_new_problem(self):
        """生成新题目"""
        self.current_a, self.current_b = self.generate_decimal_numbers()
        operators = self.get_selected_operators()
        self.current_op = random.choice(operators)
        
        # 确保减法结果为正
        if self.current_op == '-' and self.current_a < self.current_b:
            self.current_a, self.current_b = self.current_b, self.current_a
        
        # 计算答案
        if self.current_op == '+':
            self.correct_answer = self.current_a + self.current_b
        elif self.current_op == '-':
            self.correct_answer = self.current_a - self.current_b
        elif self.current_op == '×':
            self.correct_answer = self.current_a * self.current_b
        elif self.current_op == '÷':
            self.correct_answer = self.current_a / self.current_b
        
        # 显示题目
        problem_text = f"{self.current_a} {self.current_op} {self.current_b} = ?"
        self.problem_label.setText(problem_text)
        self.problem_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 20px;
                border-radius: 10px;
            }
        """)
        
        self.answer_input.clear()
        self.result_label.clear()
        self.answer_input.setFocus()
    
    def check_answer(self):
        """检查答案"""
        try:
            user_answer = float(self.answer_input.text())
            decimal_places = self.decimal_places_spinbox.value()
            correct_answer = round(self.correct_answer, decimal_places)
            
            self.total_questions += 1
            
            if abs(user_answer - correct_answer) < 0.001:
                self.score += 1
                self.result_label.setText(f"🎉 正确！答案是 {correct_answer}")
                self.result_label.setStyleSheet("QLabel { color: #4CAF50; background-color: #E8F5E8; padding: 10px; border-radius: 5px; }")
            else:
                self.result_label.setText(f"❌ 错误！正确答案是 {correct_answer}")
                self.result_label.setStyleSheet("QLabel { color: #F44336; background-color: #FFEBEE; padding: 10px; border-radius: 5px; }")
            
            # 更新状态
            accuracy = (self.score / self.total_questions * 100) if self.total_questions > 0 else 0
            self.score_label.setText(f"得分: {self.score}")
            self.accuracy_label.setText(f"正确率: {accuracy:.1f}%")
            
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的数字！")
    
    def generate_excel(self):
        """生成Excel文件"""
        try:
            total_problems = self.total_problems_spinbox.value()
            group_size = self.group_size_spinbox.value()
            decimal_places = self.excel_decimal_places_spinbox.value()
            
            # 获取运算符
            operators = []
            if self.excel_add_cb.isChecked():
                operators.append('+')
            if self.excel_sub_cb.isChecked():
                operators.append('-')
            if self.excel_mul_cb.isChecked():
                operators.append('×')
            if self.excel_div_cb.isChecked():
                operators.append('÷')
            
            if not operators:
                operators = ['+']
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 生成题目数据
            problems_data = []
            max_val = 9.9 if decimal_places == 1 else (9.99 if decimal_places == 2 else 9.999)
            
            for i in range(total_problems):
                a = round(random.uniform(0.1, max_val), decimal_places)
                b = round(random.uniform(0.1, max_val), decimal_places)
                op = random.choice(operators)
                
                if op == '-' and a < b:
                    a, b = b, a
                
                problems_data.append([a, op, b, '='])
                
                # 更新进度
                progress = int((i + 1) / total_problems * 50)
                self.progress_bar.setValue(progress)
                QApplication.processEvents()
            
            # 创建Excel数据结构 - 每个题目占用5个单元格：a + b = 结果
            final_data = {}
            num_groups = (total_problems + group_size - 1) // group_size

            for group in range(num_groups):
                start_idx = group * group_size
                end_idx = min(start_idx + group_size, total_problems)
                group_problems = problems_data[start_idx:end_idx]

                # 补齐不足的题目
                while len(group_problems) < group_size:
                    group_problems.append(['', '', '', '', ''])

                # 计算每个题目的结果
                complete_problems = []
                for p in group_problems:
                    if p[0] != '':  # 如果不是空题目
                        a, op, b = p[0], p[1], p[2]
                        # 计算结果
                        if op == '+':
                            result = round(a + b, decimal_places)
                        elif op == '-':
                            result = round(a - b, decimal_places)
                        elif op == '×':
                            result = round(a * b, decimal_places)
                        elif op == '÷':
                            result = round(a / b, decimal_places)
                        else:
                            result = ''
                        complete_problems.append([a, op, b, '=', result])
                    else:
                        complete_problems.append(['', '', '', '', ''])

                # 创建5列：第一个数、运算符、第二个数、等号、结果
                col_base = f"第{group+1}组"
                final_data[f'{col_base}_数字A'] = [p[0] for p in complete_problems]
                final_data[f'{col_base}_运算符'] = [p[1] for p in complete_problems]
                final_data[f'{col_base}_数字B'] = [p[2] for p in complete_problems]
                final_data[f'{col_base}_等号'] = [p[3] for p in complete_problems]
                final_data[f'{col_base}_结果'] = [p[4] for p in complete_problems]

                # 添加空白列分隔不同组
                if group < num_groups - 1:
                    final_data[f'空白_{group+1}'] = ['' for _ in range(group_size)]

                # 更新进度
                progress = 50 + int((group + 1) / num_groups * 40)
                self.progress_bar.setValue(progress)
                QApplication.processEvents()
            
            # 创建DataFrame并保存
            df = pd.DataFrame(final_data)
            
            operators_str = ''.join(operators)
            default_filename = f'美化版小数练习_{decimal_places}位_{operators_str}_{total_problems}题.xlsx'
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "保存Excel文件", default_filename,
                "Excel files (*.xlsx);;All files (*.*)"
            )
            
            if filename:
                df.to_excel(filename, index=False)
                self.progress_bar.setValue(100)
                
                QMessageBox.information(
                    self, "🎉 生成成功", 
                    f"已成功生成{total_problems}道题目！\n\n"
                    f"📁 文件：{filename}\n"
                    f"🔢 小数位数：{decimal_places}位\n"
                    f"➕ 运算符：{', '.join(operators)}\n"
                    f"📊 分组：每{group_size}题一组，共{num_groups}组"
                )
            
            self.progress_bar.setVisible(False)
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "❌ 生成失败", f"生成Excel文件时出错：\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = BeautifulCalculator()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
